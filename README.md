# AI-Driven Development Project

This project is a new vibe coding assistant like Augment. The user acts as the product owner and only writes plain text User Stories for Test Driven Development in a tasks like interface. Using user stories, bugs, and versioning the AI determines what to change in each iteration.

## Project Structure

- `frontend/`: Contains the React frontend application.
- `backend/`: Contains the Node.js backend application.
- `tasks/`: Stores user stories.
- `reports/`: Stores reports generated by the AI Scrum Master.
