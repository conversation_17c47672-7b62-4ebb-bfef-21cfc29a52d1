* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f7f7f8;
  min-height: 100vh;
}

#root {
  min-height: 100vh;
}

.App {
  min-height: 100vh;
}

/* Global button styles */
button {
  font-family: inherit;
}

/* Global form styles */
input, textarea, select {
  font-family: inherit;
}

/* Global scrollbar styling - consistent dark theme */
::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #374151;
  border-radius: 6px;
  border: 2px solid transparent;
  background-clip: content-box;
}

::-webkit-scrollbar-thumb:hover {
  background: #4b5563;
  background-clip: content-box;
}

::-webkit-scrollbar-corner {
  background: transparent;
}

/* Firefox scrollbar styling */
* {
  scrollbar-width: thin;
  scrollbar-color: #374151 transparent;
}
