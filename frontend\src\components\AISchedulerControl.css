.ai-scheduler-control {
  flex: 1;
  background: #1a1a1a;
  color: #e5e5e5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow-y: auto;
}

.ai-scheduler-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 16px;
  color: #9ca3af;
}

.scheduler-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #2d2d2d;
  background: #1a1a1a;
}

.scheduler-header h2 {
  margin: 0;
  color: #ffffff;
  font-size: 20px;
  font-weight: 600;
}

.scheduler-status {
  display: flex;
  align-items: center;
  gap: 10px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.status-text {
  font-weight: 500;
  font-size: 14px;
  color: #e5e5e5;
}

.scheduler-content {
  padding: 20px 24px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Full Width Statistics Section */
.stats-section-full {
  background: linear-gradient(135deg, #2d2d2d 0%, #1a1a1a 100%);
  border: 1px solid #374151;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 8px;
}

.stats-section-full h3 {
  margin: 0 0 20px 0;
  color: #ffffff;
  font-size: 18px;
  font-weight: 700;
  text-align: center;
  letter-spacing: 0.5px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.stat-card-modern {
  background: linear-gradient(135deg, #374151 0%, #2d2d2d 100%);
  border: 1px solid #4b5563;
  border-radius: 10px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #10b981, #f59e0b, #ef4444, #8b5cf6);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-card-modern:hover {
  transform: translateY(-2px);
  border-color: #6b7280;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.stat-card-modern:hover::before {
  opacity: 1;
}

.stat-card-modern.last-activity {
  grid-column: 1 / -1;
  max-width: 400px;
  margin: 0 auto;
}

.stat-icon {
  flex-shrink: 0;
}

.icon-circle {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
  color: #ffffff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.icon-circle.blue {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.icon-circle.green {
  background: linear-gradient(135deg, #10b981, #047857);
}

.icon-circle.red {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.icon-circle.orange {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.icon-circle.purple {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.stat-content {
  flex: 1;
  min-width: 0;
}

.stat-value-large {
  font-size: 28px;
  font-weight: 800;
  color: #ffffff;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-value-time {
  font-size: 14px;
  font-weight: 600;
  color: #e5e5e5;
  line-height: 1.2;
  margin-bottom: 4px;
}

.stat-label-modern {
  font-size: 13px;
  font-weight: 500;
  color: #9ca3af;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Horizontal Sections */
.horizontal-section {
  background: #2d2d2d;
  border: 1px solid #374151;
  border-radius: 8px;
  padding: 16px;
}

.horizontal-section h3 {
  margin: 0 0 12px 0;
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
}

.horizontal-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.horizontal-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: #1a1a1a;
  border: 1px solid #374151;
  border-radius: 6px;
  transition: border-color 0.15s ease;
}

.horizontal-item:hover {
  border-color: #4b5563;
}

.item-name {
  font-weight: 500;
  color: #e5e5e5;
  font-size: 14px;
  min-width: 120px;
}

.item-status {
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  min-width: 80px;
  text-align: center;
}

.item-status.enabled {
  background-color: #065f46;
  color: #10b981;
}

.item-status.disabled {
  background-color: #7f1d1d;
  color: #f87171;
}

.item-project {
  color: #9ca3af;
  font-size: 13px;
  font-style: italic;
}

.item-dates {
  color: #9ca3af;
  font-size: 12px;
  margin-left: auto;
}

.empty-state {
  color: #9ca3af;
  font-style: italic;
  text-align: center;
  padding: 20px;
}



.scheduler-controls {
  background: #2d2d2d;
  border: 1px solid #374151;
  border-radius: 8px;
  padding: 20px;
  margin: 0 24px 24px 24px;
}

.control-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.btn {
  padding: 8px 16px;
  border: 1px solid transparent;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 13px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-success {
  background-color: #059669;
  color: white;
  border-color: #059669;
}

.btn-success:hover:not(:disabled) {
  background-color: #047857;
  border-color: #047857;
}

.btn-danger {
  background-color: #dc2626;
  color: white;
  border-color: #dc2626;
}

.btn-danger:hover:not(:disabled) {
  background-color: #b91c1c;
  border-color: #b91c1c;
}

.btn-primary {
  background-color: #2563eb;
  color: white;
  border-color: #2563eb;
}

.btn-primary:hover:not(:disabled) {
  background-color: #1d4ed8;
  border-color: #1d4ed8;
}

.config-section {
  border-top: 1px solid #374151;
  padding-top: 20px;
}

.config-section h3 {
  margin: 0 0 16px 0;
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
}

.config-group {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  gap: 12px;
}

.config-group label {
  min-width: 180px;
  font-weight: 400;
  color: #e5e5e5;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
}

.config-group input[type="number"],
.config-group select {
  padding: 6px 10px;
  border: 1px solid #374151;
  border-radius: 4px;
  font-size: 13px;
  min-width: 100px;
  background: #1a1a1a;
  color: #e5e5e5;
}

.config-group input[type="number"]:focus,
.config-group select:focus {
  outline: none;
  border-color: #4b5563;
}

.config-group input[type="number"]:disabled,
.config-group select:disabled {
  background-color: #374151;
  color: #9ca3af;
  opacity: 0.6;
}

.config-group input[type="checkbox"] {
  margin: 0;
  transform: scale(1.1);
  accent-color: #2563eb;
}

.config-group input[type="checkbox"]:disabled {
  opacity: 0.5;
}

/* Responsive design */
@media (max-width: 768px) {
  .scheduler-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 16px 20px;
  }

  .scheduler-stats {
    grid-template-columns: 1fr;
    padding: 16px 20px;
  }

  .stat-grid {
    grid-template-columns: 1fr;
  }

  .scheduler-controls {
    margin: 0 20px 20px 20px;
    padding: 16px;
  }

  .control-buttons {
    flex-direction: column;
    gap: 8px;
  }

  .config-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }

  .config-group label {
    min-width: auto;
  }
}

/* Projects Section with AI Work */
.projects-section {
  background: #2d2d2d;
  border: 1px solid #374151;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.projects-section h3 {
  margin: 0 0 16px 0;
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
}

.projects-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.project-card {
  background: #1a1a1a;
  border-radius: 8px;
  border: 1px solid #374151;
  overflow: hidden;
  transition: all 0.3s ease;
}

.project-card:hover {
  border-color: #4a5568;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #333333;
  border-bottom: 1px solid #374151;
}

.project-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.project-name {
  font-weight: 600;
  color: #ffffff;
  font-size: 14px;
}

.project-status.enabled {
  color: #4CAF50;
  font-size: 12px;
  font-weight: 500;
}

.project-status.disabled {
  color: #f44336;
  font-size: 12px;
  font-weight: 500;
}

.project-work-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.work-active {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #4CAF50;
  font-size: 12px;
  font-weight: 500;
}

.work-idle {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #9ca3af;
  font-size: 12px;
  font-weight: 500;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Sprints Container */
.sprints-container {
  padding: 16px;
  background: #1a1a1a;
}

.sprint-card {
  background: #2d2d2d;
  border-radius: 6px;
  border: 1px solid #374151;
  margin-bottom: 12px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.sprint-card:hover {
  border-color: #4a5568;
  background: #333333;
}

.sprint-card:last-child {
  margin-bottom: 0;
}

.sprint-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #374151;
  border-bottom: 1px solid #4a5568;
}

.sprint-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.sprint-name {
  font-weight: 600;
  color: #ffffff;
  font-size: 13px;
}

.sprint-dates {
  color: #9ca3af;
  font-size: 11px;
}

.sprint-work-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
}

.sprint-work-indicator .work-active {
  color: #4CAF50;
  font-size: 11px;
  font-weight: 500;
}

.sprint-work-indicator .work-idle {
  color: #9ca3af;
  font-size: 11px;
  font-weight: 500;
}

/* AI Work Items */
.ai-work-items {
  padding: 12px 16px;
}

.work-item {
  padding: 12px 16px;
  background: #2d2d2d;
  border-radius: 6px;
  border-left: 3px solid #4CAF50;
  margin-bottom: 8px;
  transition: all 0.2s ease;
}

.work-item:hover {
  background: #333333;
  border-left-color: #66BB6A;
}

.work-item:last-child {
  margin-bottom: 0;
}

.work-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.work-item-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.work-item-title {
  font-weight: 500;
  color: #ffffff;
  font-size: 13px;
}

.work-item-type {
  padding: 2px 8px;
  background: #4a5568;
  color: #e2e8f0;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
}

.work-item-time {
  color: #9ca3af;
  font-size: 11px;
}

.work-item-description {
  color: #cbd5e0;
  font-size: 12px;
  line-height: 1.4;
  margin-top: 4px;
}

/* Priority Icons */
.priority-critical {
  color: #f56565;
}

.priority-high {
  color: #ed8936;
}

.priority-medium {
  color: #4299e1;
}

.priority-low {
  color: #48bb78;
}
