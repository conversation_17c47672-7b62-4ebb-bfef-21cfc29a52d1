import React, { useState, useEffect } from 'react';
import { Clock, Play, Pause } from 'lucide-react';
import './CountdownTimer.css';

const CountdownTimer = ({ targetTime, isRunning, tickRate }) => {
  const [timeRemaining, setTimeRemaining] = useState(0);
  const [isExpired, setIsExpired] = useState(false);

  useEffect(() => {
    if (!targetTime || !isRunning) {
      setTimeRemaining(0);
      setIsExpired(false);
      return;
    }

    const updateTimer = () => {
      const now = Date.now();
      const remaining = Math.max(0, targetTime - now);
      setTimeRemaining(remaining);
      setIsExpired(remaining === 0);
    };

    // Update immediately
    updateTimer();

    // Update every second
    const interval = setInterval(updateTimer, 1000);

    return () => clearInterval(interval);
  }, [targetTime, isRunning]);

  const formatTime = (ms) => {
    if (ms === 0) return '00:00:00';
    
    const totalSeconds = Math.floor(ms / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  const formatTickRate = (ms) => {
    const totalMinutes = Math.floor(ms / (1000 * 60));
    if (totalMinutes >= 60) {
      const hours = Math.floor(totalMinutes / 60);
      const remainingMinutes = totalMinutes % 60;
      return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
    }
    return `${totalMinutes}m`;
  };

  const getProgressPercentage = () => {
    if (!tickRate || timeRemaining === 0) return 0;
    return Math.max(0, Math.min(100, ((tickRate - timeRemaining) / tickRate) * 100));
  };

  if (!isRunning) {
    return (
      <div className="countdown-timer stopped">
        <div className="timer-icon">
          <Pause size={32} />
        </div>
        <div className="timer-content">
          <h2>AI Scheduler Stopped</h2>
          <p>Start the scheduler to see the next tick countdown</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`countdown-timer ${isExpired ? 'expired' : 'running'}`}>
      <div className="timer-icon">
        <Clock size={32} />
      </div>
      <div className="timer-content">
        <div className="timer-header">
          <h2>Next AI Processing Tick</h2>
          <span className="tick-interval">Every {formatTickRate(tickRate)}</span>
        </div>
        <div className="timer-display">
          <span className="time-value">{formatTime(timeRemaining)}</span>
          {isExpired && <span className="expired-label">Processing...</span>}
        </div>
        <div className="progress-bar">
          <div 
            className="progress-fill" 
            style={{ width: `${getProgressPercentage()}%` }}
          ></div>
        </div>
      </div>
    </div>
  );
};

export default CountdownTimer;
