import React, { useState } from 'react';
import {
  X,
  Edit,
  User,
  Calendar,
  Target,
  GitBranch,
  Clock,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  MessageSquare,
  Bot,
  Play,
  MessageCircle
} from 'lucide-react';
import CommentTimeline from './CommentTimeline';
import ConfirmationModal from './ConfirmationModal';
import './PropertiesPanel.css';

const PropertiesPanel = ({ story, onClose, onUpdate, onDelete }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState(story || {});
  const [showComments, setShowComments] = useState(false);
  const [aiProcessing, setAiProcessing] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [modalMessage, setModalMessage] = useState('');

  const handleSave = () => {
    onUpdate(story.id, editData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditData(story);
    setIsEditing(false);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'done': return '#10b981';
      case 'in_progress': return '#3b82f6';
      case 'backlog': return '#6b7280';
      default: return '#6b7280';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'critical': return '#ef4444';
      case 'high': return '#f59e0b';
      case 'medium': return '#6b7280';
      case 'low': return '#10b981';
      default: return '#6b7280';
    }
  };

  const getImplementationStatusIcon = (status) => {
    switch (status) {
      case 'implemented':
        return <CheckCircle className="status-icon implemented" size={16} />;
      case 'modified_after_implementation':
        return <AlertTriangle className="status-icon modified" size={16} />;
      default:
        return <Clock className="status-icon not-started" size={16} />;
    }
  };

  const triggerAiProcessing = async () => {
    setAiProcessing(true);
    try {
      const response = await fetch(`/api/projects/${story.project_id}/stories/${story.id}/process`, {
        method: 'POST'
      });

      if (response.ok) {
        const result = await response.json();
        setModalMessage(`AI processing completed! ${result.result.tasksCreated} tasks created, ${result.result.filesGenerated} files generated.`);
        setShowSuccessModal(true);
        // Refresh the story data if needed
        if (onUpdate) {
          // Trigger a refresh of the story data
          window.location.reload(); // Simple refresh for now
        }
      } else {
        const error = await response.json();
        setModalMessage(`AI processing failed: ${error.error}`);
        setShowErrorModal(true);
      }
    } catch (error) {
      console.error('Error triggering AI processing:', error);
      setModalMessage('Error triggering AI processing');
      setShowErrorModal(true);
    } finally {
      setAiProcessing(false);
    }
  };

  return (
    <div className="properties-panel">
      <div className="panel-header">
        <h3>Story Details</h3>
        <div className="header-actions">
          <button 
            className="edit-btn"
            onClick={() => setIsEditing(!isEditing)}
          >
            <Edit size={16} />
          </button>
          <button className="close-btn" onClick={onClose}>
            <X size={16} />
          </button>
        </div>
      </div>

      <div className="panel-content">
        {/* Story ID and Title */}
        <div className="property-section">
          <div className="story-header">
            <span className="story-id">#{story.id.slice(-6)}</span>
            {isEditing ? (
              <input
                type="text"
                value={editData.title || ''}
                onChange={(e) => setEditData({...editData, title: e.target.value})}
                className="title-input"
              />
            ) : (
              <h4 className="story-title">{story.title}</h4>
            )}
          </div>
        </div>

        {/* Properties Grid */}
        <div className="properties-grid">
          {/* Status */}
          <div className="property-item">
            <label>Status</label>
            {isEditing ? (
              <select
                value={editData.status || ''}
                onChange={(e) => setEditData({...editData, status: e.target.value})}
                className="property-select"
              >
                <option value="backlog">Backlog</option>
                <option value="in_progress">In Progress</option>
                <option value="done">Done</option>
              </select>
            ) : (
              <div className="property-value">
                <div 
                  className="status-indicator"
                  style={{ backgroundColor: getStatusColor(story.status) }}
                />
                {story.status?.replace('_', ' ') || 'Not set'}
              </div>
            )}
          </div>

          {/* Priority */}
          <div className="property-item">
            <label>Priority</label>
            {isEditing ? (
              <select
                value={editData.priority || ''}
                onChange={(e) => setEditData({...editData, priority: e.target.value})}
                className="property-select"
              >
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
                <option value="critical">Critical</option>
              </select>
            ) : (
              <div className="property-value">
                <div 
                  className="priority-indicator"
                  style={{ backgroundColor: getPriorityColor(story.priority) }}
                />
                {story.priority || 'Not set'}
              </div>
            )}
          </div>

          {/* Story Points */}
          <div className="property-item">
            <label>Story Points</label>
            {isEditing ? (
              <select
                value={editData.story_points || ''}
                onChange={(e) => setEditData({...editData, story_points: parseInt(e.target.value)})}
                className="property-select"
              >
                <option value="1">1</option>
                <option value="2">2</option>
                <option value="3">3</option>
                <option value="5">5</option>
                <option value="8">8</option>
                <option value="13">13</option>
              </select>
            ) : (
              <div className="property-value">
                <BarChart3 size={16} />
                {story.story_points || 'Not set'}
              </div>
            )}
          </div>

          {/* Epic */}
          <div className="property-item">
            <label>Epic</label>
            {isEditing ? (
              <input
                type="text"
                value={editData.epic || ''}
                onChange={(e) => setEditData({...editData, epic: e.target.value})}
                className="property-input"
                placeholder="Enter epic name"
              />
            ) : (
              <div className="property-value">
                <Target size={16} />
                {story.epic || 'No epic'}
              </div>
            )}
          </div>

          {/* User Persona */}
          <div className="property-item">
            <label>User Persona</label>
            <div className="property-value">
              <User size={16} />
              {story.user_persona || 'End User'}
            </div>
          </div>

          {/* Implementation Status */}
          <div className="property-item">
            <label>Implementation</label>
            <div className="property-value">
              {getImplementationStatusIcon(story.implementation_status)}
              {story.implementation_status?.replace('_', ' ') || 'Not Started'}
            </div>
          </div>

          {/* Version */}
          {story.implemented_in_version && (
            <div className="property-item">
              <label>Version</label>
              <div className="property-value">
                <GitBranch size={16} />
                v{story.implemented_in_version}
              </div>
            </div>
          )}

          {/* Created Date */}
          <div className="property-item">
            <label>Created</label>
            <div className="property-value">
              <Calendar size={16} />
              {story.created_at ? new Date(story.created_at).toLocaleDateString() : 'Unknown'}
            </div>
          </div>
        </div>

        {/* Description */}
        <div className="property-section">
          <label>Description</label>
          {isEditing ? (
            <textarea
              value={editData.description || ''}
              onChange={(e) => setEditData({...editData, description: e.target.value})}
              className="description-textarea"
              rows={4}
              placeholder="Enter story description"
            />
          ) : (
            <div className="description-content">
              {story.description || 'No description provided'}
            </div>
          )}
        </div>

        {/* Acceptance Criteria */}
        <div className="property-section">
          <label>Acceptance Criteria</label>
          {isEditing ? (
            <textarea
              value={editData.acceptance_criteria || ''}
              onChange={(e) => setEditData({...editData, acceptance_criteria: e.target.value})}
              className="description-textarea"
              rows={4}
              placeholder="Enter acceptance criteria"
            />
          ) : (
            <div className="description-content">
              {story.acceptance_criteria || 'No acceptance criteria provided'}
            </div>
          )}
        </div>

        {/* Business Value */}
        {story.business_value && (
          <div className="property-section">
            <label>Business Value</label>
            <div className="description-content">
              {story.business_value}
            </div>
          </div>
        )}



        {/* Action Buttons */}
        {isEditing ? (
          <div className="action-buttons">
            <button className="btn-primary" onClick={handleSave}>
              Save Changes
            </button>
            <button className="btn-secondary" onClick={handleCancel}>
              Cancel
            </button>
          </div>
        ) : (
          <div className="action-buttons">
            <button className="btn-danger" onClick={() => onDelete(story.id)}>
              Delete Story
            </button>
          </div>
        )}
      </div>

      {/* Success Modal */}
      <ConfirmationModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        onConfirm={() => setShowSuccessModal(false)}
        title="Success"
        message={modalMessage}
        confirmText="OK"
        cancelText=""
        type="default"
      />

      {/* Error Modal */}
      <ConfirmationModal
        isOpen={showErrorModal}
        onClose={() => setShowErrorModal(false)}
        onConfirm={() => setShowErrorModal(false)}
        title="Error"
        message={modalMessage}
        confirmText="OK"
        cancelText=""
        type="danger"
      />
    </div>
  );
};

export default PropertiesPanel;
