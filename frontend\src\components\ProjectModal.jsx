import { useState, useEffect } from 'react';
import { X, Calendar, User, Target, Code, Bot, Settings } from 'lucide-react';
import './ProjectModal.css';

const ProjectModal = ({ isOpen, onClose, onSave, project = null }) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    start_date: '',
    end_date: '',
    status: 'planning',
    progress: 0,
    tech_stack: 'react',
    ai_enabled: true,
    ai_config: {
      auto_testing: true,
      code_review: true,
      documentation: true
    },
    repository_url: ''
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (project) {
      setFormData({
        name: project.name || '',
        description: project.description || '',
        start_date: project.start_date || '',
        end_date: project.end_date || '',
        status: project.status || 'planning',
        progress: project.progress || 0,
        tech_stack: project.tech_stack || 'react',
        ai_enabled: project.ai_enabled !== undefined ? project.ai_enabled : true,
        ai_config: project.ai_config ? JSON.parse(project.ai_config) : {
          auto_testing: true,
          code_review: true,
          documentation: true
        },
        repository_url: project.repository_url || ''
      });
    } else {
      setFormData({
        name: '',
        description: '',
        start_date: '',
        end_date: '',
        status: 'planning',
        progress: 0,
        tech_stack: 'react',
        ai_enabled: true,
        ai_config: {
          auto_testing: true,
          code_review: true,
          documentation: true
        },
        repository_url: ''
      });
    }
    setErrors({});
  }, [project, isOpen]);

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Project name is required';
    }

    if (!formData.tech_stack) {
      newErrors.tech_stack = 'Tech stack is required';
    }

    if (formData.start_date && formData.end_date) {
      if (new Date(formData.start_date) >= new Date(formData.end_date)) {
        newErrors.end_date = 'End date must be after start date';
      }
    }
    
    if (formData.progress < 0 || formData.progress > 100) {
      newErrors.progress = 'Progress must be between 0 and 100';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setLoading(true);
    
    try {
      const url = project 
        ? `http://localhost:3000/projects/${project.id}`
        : 'http://localhost:3000/projects';
      
      const method = project ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      
      if (!response.ok) {
        throw new Error('Failed to save project');
      }
      
      const savedProject = await response.json();
      onSave(savedProject);
      onClose();
    } catch (error) {
      console.error('Error saving project:', error);
      setErrors({ submit: 'Failed to save project. Please try again.' });
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={e => e.stopPropagation()}>
        <div className="modal-header">
          <h2>
            <Target size={24} />
            {project ? 'Edit Project' : 'Create New Project'}
          </h2>
          <button className="modal-close" onClick={onClose}>
            <X size={24} />
          </button>
        </div>
        
        <form onSubmit={handleSubmit} className="project-form">
          <div className="form-group">
            <label htmlFor="name">Project Name *</label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className={errors.name ? 'error' : ''}
              placeholder="Enter project name"
            />
            {errors.name && <span className="error-message">{errors.name}</span>}
          </div>
          
          <div className="form-group">
            <label htmlFor="description">Description</label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              placeholder="Enter project description"
              rows={3}
            />
          </div>
          
          <div className="form-row">
            <div className="form-group">
              <label htmlFor="start_date">
                <Calendar size={16} />
                Start Date
              </label>
              <input
                type="date"
                id="start_date"
                name="start_date"
                value={formData.start_date}
                onChange={handleChange}
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="end_date">
                <Calendar size={16} />
                End Date
              </label>
              <input
                type="date"
                id="end_date"
                name="end_date"
                value={formData.end_date}
                onChange={handleChange}
                className={errors.end_date ? 'error' : ''}
              />
              {errors.end_date && <span className="error-message">{errors.end_date}</span>}
            </div>
          </div>
          
          <div className="form-group">
            <label htmlFor="tech_stack">
              <Code size={16} />
              Tech Stack *
            </label>
            <select
              id="tech_stack"
              name="tech_stack"
              value={formData.tech_stack}
              onChange={handleChange}
              className={errors.tech_stack ? 'error' : ''}
            >
              <option value="react">React + Node.js</option>
              <option value="vue">Vue.js + Node.js</option>
              <option value="angular">Angular + Node.js</option>
              <option value="python">Python + Django/Flask</option>
              <option value="java">Java + Spring Boot</option>
              <option value="dotnet">.NET Core</option>
              <option value="php">PHP + Laravel</option>
              <option value="ruby">Ruby on Rails</option>
              <option value="go">Go + Gin/Echo</option>
              <option value="rust">Rust + Actix/Warp</option>
            </select>
            {errors.tech_stack && <span className="error-message">{errors.tech_stack}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="repository_url">Repository URL (Optional)</label>
            <input
              type="url"
              id="repository_url"
              name="repository_url"
              value={formData.repository_url}
              onChange={handleChange}
              placeholder="https://github.com/username/repo"
            />
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="status">Status</label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleChange}
              >
                <option value="planning">Planning</option>
                <option value="active">Active</option>
                <option value="on_hold">On Hold</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="progress">Progress (%)</label>
              <input
                type="number"
                id="progress"
                name="progress"
                value={formData.progress}
                onChange={handleChange}
                min="0"
                max="100"
                className={errors.progress ? 'error' : ''}
              />
              {errors.progress && <span className="error-message">{errors.progress}</span>}
            </div>
          </div>

          <div className="ai-section">
            <h3>
              <Bot size={20} />
              AI Development Team
            </h3>

            <div className="form-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  name="ai_enabled"
                  checked={formData.ai_enabled}
                  onChange={(e) => setFormData(prev => ({ ...prev, ai_enabled: e.target.checked }))}
                />
                Enable AI Development Team
              </label>
              <p className="help-text">AI agents will automatically work on user stories and generate code</p>
            </div>

            {formData.ai_enabled && (
              <div className="ai-config">
                <h4>
                  <Settings size={16} />
                  AI Configuration
                </h4>

                <div className="checkbox-group">
                  <label className="checkbox-label">
                    <input
                      type="checkbox"
                      checked={formData.ai_config.auto_testing}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        ai_config: { ...prev.ai_config, auto_testing: e.target.checked }
                      }))}
                    />
                    Automatic Test Generation
                  </label>

                  <label className="checkbox-label">
                    <input
                      type="checkbox"
                      checked={formData.ai_config.code_review}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        ai_config: { ...prev.ai_config, code_review: e.target.checked }
                      }))}
                    />
                    AI Code Review
                  </label>

                  <label className="checkbox-label">
                    <input
                      type="checkbox"
                      checked={formData.ai_config.documentation}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        ai_config: { ...prev.ai_config, documentation: e.target.checked }
                      }))}
                    />
                    Auto Documentation
                  </label>
                </div>
              </div>
            )}
          </div>
          
          {errors.submit && (
            <div className="error-message submit-error">{errors.submit}</div>
          )}
          
          <div className="modal-actions">
            <button type="button" onClick={onClose} className="btn-secondary">
              Cancel
            </button>
            <button type="submit" disabled={loading} className="btn-primary">
              {loading ? 'Saving...' : (project ? 'Update Project' : 'Create Project')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ProjectModal;
