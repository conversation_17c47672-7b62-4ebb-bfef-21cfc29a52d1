const { AIAgentManager } = require('./ai-agents');
const { AICommentManager } = require('./ai-comments');
const { AcceptanceCriteriaValidator } = require('./acceptance-criteria-validator');
const db = require('./database');

class AIProcessingScheduler {
  constructor() {
    this.aiAgentManager = new AIAgentManager();
    this.commentManager = new AICommentManager();
    this.acceptanceCriteriaValidator = new AcceptanceCriteriaValidator();
    this.isRunning = false;
    this.intervalId = null;
    this.config = {
      tickRate: 3600000, // 1 hour default (changed from 30 seconds for hourly checks)
      maxConcurrentProjects: 3,
      workingHours: {
        enabled: false,
        start: '09:00',
        end: '17:00',
        timezone: 'UTC'
      },
      guardrails: {
        requireActiveProject: true,
        requireActiveSprint: true,
        respectSprintDates: true,
        maxStoriesPerTick: 5, // Increased for hourly processing
        cooldownBetweenStories: 2000, // Reduced cooldown for hourly batch processing
        minSprintDaysRemaining: 1, // Don't start new work if sprint ends in 1 day
        maxConcurrentStoriesPerProject: 5, // Increased for validation processing
        priorityThreshold: 'medium', // Only process medium+ priority stories
        blockOnCriticalIssues: true,
        requireMinimumSprintCapacity: 0.2, // 20% capacity remaining
        enableAcceptanceCriteriaValidation: true, // New feature flag
        validationScoreThreshold: 70 // Minimum score to pass validation
      }
    };
    this.stats = {
      totalTicks: 0,
      storiesProcessed: 0,
      storiesValidated: 0,
      validationsPassed: 0,
      validationsFailed: 0,
      lastActivity: null,
      errors: 0,
      skippedTicks: 0
    };
  }

  /**
   * Start the AI processing scheduler
   */
  async start(customConfig = {}) {
    if (this.isRunning) {
      console.log('⚠️ AI Scheduler is already running');
      return false;
    }

    // Merge custom config
    this.config = { ...this.config, ...customConfig };
    
    console.log(`🚀 Starting AI Processing Scheduler with ${this.config.tickRate}ms tick rate`);

    // Log initial startup (skip comment logging to avoid foreign key issues)
    console.log(`📊 Scheduler Config: ${JSON.stringify(this.config, null, 2)}`);

    this.isRunning = true;
    this.stats.startTime = Date.now();
    this.intervalId = setInterval(() => {
      this.processTick().catch(error => {
        console.error('❌ Error in AI scheduler tick:', error);
        this.stats.errors++;
      });
    }, this.config.tickRate);

    return true;
  }

  /**
   * Stop the AI processing scheduler
   */
  async stop() {
    if (!this.isRunning) {
      console.log('⚠️ AI Scheduler is not running');
      return false;
    }

    console.log('🛑 Stopping AI Processing Scheduler');
    
    this.isRunning = false;
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }

    // Log shutdown (skip comment logging to avoid foreign key issues)
    console.log(`📊 Final Stats: ${this.stats.storiesProcessed} stories processed, ${this.stats.totalTicks} ticks, ${this.stats.errors} errors`);

    return true;
  }

  /**
   * Main processing tick - checks guardrails and processes work
   */
  async processTick() {
    this.stats.totalTicks++;
    console.log(`🔄 AI Scheduler Tick #${this.stats.totalTicks}`);

    try {
      // Check working hours guardrail
      if (!this.isWithinWorkingHours()) {
        console.log('⏰ Outside working hours, skipping tick');
        this.stats.skippedTicks++;
        return;
      }

      // Get active projects with guardrails
      const activeProjects = await this.getActiveProjects();
      
      if (activeProjects.length === 0) {
        console.log('📋 No active projects found, skipping tick');
        this.stats.skippedTicks++;
        return;
      }

      console.log(`📊 Found ${activeProjects.length} active projects`);

      // Process each active project
      for (const project of activeProjects.slice(0, this.config.maxConcurrentProjects)) {
        await this.processProject(project);
        
        // Cooldown between projects
        if (this.config.guardrails.cooldownBetweenStories > 0) {
          await this.sleep(this.config.guardrails.cooldownBetweenStories);
        }
      }

      this.stats.lastActivity = new Date().toISOString();

    } catch (error) {
      console.error('❌ Error in processTick:', error);
      this.stats.errors++;
      
      // Log error (skip comment logging to avoid foreign key issues)
      console.error(`❌ Processing tick error: ${error.message}`, error.stack);
    }
  }

  /**
   * Process work for a specific project
   */
  async processProject(project) {
    console.log(`🏗️ Processing project: ${project.name} (${project.id})`);

    try {
      // Check if project AI processing is enabled
      if (!project.ai_processing_enabled) {
        console.log(`🚫 AI processing disabled for project ${project.name}, skipping`);
        return;
      }

      // Check if project has active sprint
      const activeSprint = await this.getActiveSprint(project.id);

      if (!activeSprint && this.config.guardrails.requireActiveSprint) {
        console.log(`⏭️ Project ${project.name} has no active sprint, skipping`);
        return;
      }

      // Check sprint engagement rules
      if (activeSprint && !this.shouldEngageWithSprint(activeSprint)) {
        console.log(`⏰ Sprint conditions not met for project ${project.name}, skipping`);
        return;
      }

      // Check for critical blockers at project level
      if (this.config.guardrails.blockOnCriticalIssues) {
        const projectBlockers = await this.commentManager.getBlockers(project.id, 'project', project.id);
        const criticalBlockers = projectBlockers.filter(b => b.priority === 'critical');

        if (criticalBlockers.length > 0) {
          console.log(`🚨 Project ${project.name} has ${criticalBlockers.length} critical blockers, skipping`);
          return;
        }
      }

      // Get pending work for this project (implementation and validation)
      const pendingStories = await this.getPendingWork(project.id, activeSprint?.id);
      const validationStories = await this.getStoriesNeedingValidation(project.id, activeSprint?.id);

      if (pendingStories.length === 0 && validationStories.length === 0) {
        console.log(`✅ Project ${project.name} has no pending work or validation needed`);
        return;
      }

      // Filter stories by engagement rules
      const eligibleStories = this.filterStoriesByEngagementRules(pendingStories);
      const eligibleValidationStories = this.filterStoriesByEngagementRules(validationStories);

      const totalEligible = eligibleStories.length + eligibleValidationStories.length;
      if (totalEligible === 0) {
        console.log(`📋 Project ${project.name} has no eligible stories after applying engagement rules`);
        return;
      }

      console.log(`📝 Found ${eligibleStories.length} stories for implementation and ${eligibleValidationStories.length} for validation in project ${project.name}`);

      // Check concurrent processing limit
      const currentlyProcessing = await this.getCurrentlyProcessingStories(project.id);
      const availableSlots = this.config.guardrails.maxConcurrentStoriesPerProject - currentlyProcessing;

      if (availableSlots <= 0) {
        console.log(`⏸️ Project ${project.name} has reached concurrent processing limit, skipping`);
        return;
      }

      // Process stories up to the available slots and tick limit
      const maxToProcess = Math.min(
        availableSlots,
        this.config.guardrails.maxStoriesPerTick,
        totalEligible
      );

      // Prioritize validation over new implementation
      const allStoriesToProcess = [
        ...eligibleValidationStories.map(s => ({ ...s, processType: 'validation' })),
        ...eligibleStories.map(s => ({ ...s, processType: 'implementation' }))
      ].slice(0, maxToProcess);

      for (const story of allStoriesToProcess) {
        if (story.processType === 'validation') {
          await this.validateStory(project.id, story, project.workspace_path);
        } else {
          await this.processStory(project.id, story);
          this.stats.storiesProcessed++;
        }

        // Cooldown between stories
        if (this.config.guardrails.cooldownBetweenStories > 0) {
          await this.sleep(this.config.guardrails.cooldownBetweenStories);
        }
      }

    } catch (error) {
      console.error(`❌ Error processing project ${project.name}:`, error);

      // Log project-specific error
      await this.commentManager.addComment(
        project.id,
        'project',
        project.id,
        'AIProcessingScheduler',
        `Error processing project: ${error.message}`,
        'blocker',
        { error: error.message },
        { priority: 'high', isInternal: true }
      );
    }
  }

  /**
   * Process a single user story
   */
  async processStory(projectId, story) {
    console.log(`📖 Processing story: ${story.title}`);

    try {
      // Check if story has any blockers
      const blockers = await this.commentManager.getBlockers(projectId, 'user_story', story.id);

      if (blockers.length > 0) {
        console.log(`🚫 Story ${story.title} has ${blockers.length} active blockers, skipping`);
        return;
      }

      // Process the story with AI agents
      const result = await this.aiAgentManager.processUserStory(projectId, story);

      // Mark story as implemented (ready for validation in next cycle)
      await this.updateStoryImplementationStatus(story.id, 'implemented');

      console.log(`✅ Completed processing story: ${story.title}`);

      // Log successful processing
      await this.commentManager.addComment(
        projectId,
        'user_story',
        story.id,
        'AIProcessingScheduler',
        `Scheduled processing completed successfully. Generated ${result.codeStructure?.files?.length || 0} files and ${result.tasks?.length || 0} tasks. Story marked for validation in next cycle.`,
        'progress',
        {
          scheduled_processing: true,
          tick_number: this.stats.totalTicks,
          files_generated: result.codeStructure?.files?.length || 0,
          tasks_created: result.tasks?.length || 0,
          next_step: 'validation'
        }
      );

    } catch (error) {
      console.error(`❌ Error processing story ${story.title}:`, error);

      // Log story-specific error
      await this.commentManager.addComment(
        projectId,
        'user_story',
        story.id,
        'AIProcessingScheduler',
        `Scheduled processing failed: ${error.message}`,
        'blocker',
        {
          error: error.message,
          scheduled_processing: true,
          tick_number: this.stats.totalTicks
        },
        { priority: 'critical' }
      );
    }
  }

  /**
   * Validate a single user story against acceptance criteria
   */
  async validateStory(projectId, story, workspacePath) {
    console.log(`🔍 Validating story: ${story.title}`);

    try {
      // Check if validation is enabled
      if (!this.config.guardrails.enableAcceptanceCriteriaValidation) {
        console.log(`⏭️ Acceptance criteria validation disabled, skipping story ${story.title}`);
        return;
      }

      // Check if story has any blockers
      const blockers = await this.commentManager.getBlockers(projectId, 'user_story', story.id);

      if (blockers.length > 0) {
        console.log(`🚫 Story ${story.title} has ${blockers.length} active blockers, skipping validation`);
        return;
      }

      // Perform validation
      const validationResult = await this.acceptanceCriteriaValidator.validateStoryImplementation(
        story,
        projectId,
        workspacePath
      );

      // Update story status based on validation result
      const passed = validationResult.passed && validationResult.score >= this.config.guardrails.validationScoreThreshold;
      const newStatus = passed ? 'validated' : 'validation_failed';

      await this.updateStoryImplementationStatus(story.id, newStatus);

      // Update statistics
      this.stats.storiesValidated++;
      if (passed) {
        this.stats.validationsPassed++;
      } else {
        this.stats.validationsFailed++;
      }

      console.log(`${passed ? '✅' : '❌'} Validation ${passed ? 'passed' : 'failed'} for story: ${story.title} (Score: ${validationResult.score}/100)`);

      // Log validation completion
      await this.commentManager.addComment(
        projectId,
        'user_story',
        story.id,
        'AIProcessingScheduler',
        `Scheduled validation ${passed ? 'PASSED' : 'FAILED'} (Score: ${validationResult.score}/100). ${validationResult.feedback}`,
        passed ? 'success' : 'warning',
        {
          scheduled_validation: true,
          tick_number: this.stats.totalTicks,
          validation_passed: passed,
          validation_score: validationResult.score,
          validation_threshold: this.config.guardrails.validationScoreThreshold
        },
        { priority: passed ? 'normal' : 'high' }
      );

    } catch (error) {
      console.error(`❌ Error validating story ${story.title}:`, error);

      // Mark validation as failed due to error
      await this.updateStoryImplementationStatus(story.id, 'validation_failed');
      this.stats.storiesValidated++;
      this.stats.validationsFailed++;

      // Log validation error
      await this.commentManager.addComment(
        projectId,
        'user_story',
        story.id,
        'AIProcessingScheduler',
        `Scheduled validation failed due to error: ${error.message}`,
        'blocker',
        {
          error: error.message,
          scheduled_validation: true,
          tick_number: this.stats.totalTicks
        },
        { priority: 'critical' }
      );
    }
  }

  /**
   * Get active projects that meet guardrail criteria
   */
  async getActiveProjects() {
    return new Promise((resolve, reject) => {
      let query = `SELECT * FROM projects WHERE ai_enabled = 1`;
      
      if (this.config.guardrails.requireActiveProject) {
        query += ` AND status IN ('active', 'in_progress')`;
      }
      
      query += ` ORDER BY created_at ASC`;
      
      db.all(query, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows || []);
        }
      });
    });
  }

  /**
   * Get active sprint for a project
   */
  async getActiveSprint(projectId) {
    return new Promise((resolve, reject) => {
      const now = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
      
      const query = `SELECT * FROM sprints 
                     WHERE project_id = ? 
                     AND status = 'active' 
                     AND start_date <= ? 
                     AND end_date >= ?
                     ORDER BY start_date DESC 
                     LIMIT 1`;
      
      db.get(query, [projectId, now, now], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row || null);
        }
      });
    });
  }

  /**
   * Get pending work (user stories) for a project
   */
  async getPendingWork(projectId, sprintId = null) {
    return new Promise((resolve, reject) => {
      let query = `SELECT * FROM user_stories 
                   WHERE project_id = ? 
                   AND implementation_status IN ('not_started', 'modified_after_implementation')
                   AND status IN ('backlog', 'in_progress')`;
      
      const params = [projectId];
      
      if (sprintId && this.config.guardrails.requireActiveSprint) {
        query += ` AND sprint_id = ?`;
        params.push(sprintId);
      }
      
      query += ` ORDER BY priority DESC, created_at ASC`;
      
      db.all(query, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows || []);
        }
      });
    });
  }

  /**
   * Check if current time is within working hours
   */
  isWithinWorkingHours() {
    if (!this.config.workingHours.enabled) {
      return true;
    }

    const now = new Date();
    const currentTime = now.toTimeString().slice(0, 5); // HH:MM format
    
    return currentTime >= this.config.workingHours.start && 
           currentTime <= this.config.workingHours.end;
  }

  /**
   * Get current scheduler status and statistics
   */
  async getStatus() {
    const status = {
      isRunning: this.isRunning,
      config: this.config,
      stats: this.stats,
      uptime: this.isRunning ? Date.now() - (this.stats.lastActivity ? new Date(this.stats.lastActivity).getTime() : Date.now()) : 0
    };

    // Add next tick time if scheduler is running
    if (this.isRunning) {
      const now = Date.now();
      let lastTickTime;

      if (this.stats.lastActivity) {
        lastTickTime = new Date(this.stats.lastActivity).getTime();
      } else if (this.stats.startTime) {
        lastTickTime = this.stats.startTime;
      } else {
        // Fallback: assume scheduler just started
        lastTickTime = now;
      }

      const nextTickTime = lastTickTime + this.config.tickRate;
      status.nextTickTime = nextTickTime;
      status.timeUntilNextTick = Math.max(0, nextTickTime - now);
    }

    // Add active work information
    try {
      status.activeWork = await this.getActiveWork();
    } catch (error) {
      console.error('Error getting active work:', error);
      status.activeWork = [];
    }

    return status;
  }

  /**
   * Get current active AI work across all projects
   */
  async getActiveWork() {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT
          q.id as queue_id,
          q.project_id,
          q.entity_type,
          q.entity_id,
          q.status,
          q.started_at,
          q.metadata,
          p.name as project_name,
          us.title as story_title,
          us.description as story_description,
          us.priority as story_priority
        FROM ai_processing_queue q
        JOIN projects p ON q.project_id = p.id
        LEFT JOIN user_stories us ON q.entity_type = 'user_story' AND q.entity_id = us.id
        WHERE q.status = 'processing'
        ORDER BY q.started_at ASC
      `;

      db.all(query, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          // Group by project
          const workByProject = {};
          rows.forEach(row => {
            if (!workByProject[row.project_id]) {
              workByProject[row.project_id] = {
                projectId: row.project_id,
                projectName: row.project_name,
                activeItems: []
              };
            }

            workByProject[row.project_id].activeItems.push({
              queueId: row.queue_id,
              entityType: row.entity_type,
              entityId: row.entity_id,
              status: row.status,
              startedAt: row.started_at,
              title: row.story_title || `${row.entity_type} ${row.entity_id}`,
              description: row.story_description,
              priority: row.story_priority,
              metadata: row.metadata ? JSON.parse(row.metadata) : {}
            });
          });

          resolve(Object.values(workByProject));
        }
      });
    });
  }

  /**
   * Update scheduler configuration
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    console.log('⚙️ AI Scheduler configuration updated:', newConfig);
  }

  /**
   * Check if AI should engage with a sprint based on timeline and capacity
   */
  shouldEngageWithSprint(sprint) {
    const now = new Date();
    const endDate = new Date(sprint.end_date);
    const daysRemaining = Math.ceil((endDate - now) / (1000 * 60 * 60 * 24));

    // Check minimum days remaining
    if (daysRemaining < this.config.guardrails.minSprintDaysRemaining) {
      console.log(`⏰ Sprint ends in ${daysRemaining} days, below minimum threshold`);
      return false;
    }

    // Check sprint capacity
    if (this.config.guardrails.requireMinimumSprintCapacity > 0) {
      const capacityUsed = sprint.stories_completed / Math.max(sprint.stories_total, 1);
      const capacityRemaining = 1 - capacityUsed;

      if (capacityRemaining < this.config.guardrails.requireMinimumSprintCapacity) {
        console.log(`📊 Sprint capacity remaining (${(capacityRemaining * 100).toFixed(1)}%) below threshold`);
        return false;
      }
    }

    // Check if sprint AI processing is enabled
    if (!sprint.ai_processing_enabled) {
      console.log(`🚫 AI processing disabled for sprint`);
      return false;
    }

    return true;
  }

  /**
   * Filter stories based on engagement rules
   */
  filterStoriesByEngagementRules(stories) {
    return stories.filter(story => {
      // Priority threshold check
      if (this.config.guardrails.priorityThreshold) {
        const priorityOrder = { low: 1, medium: 2, high: 3, critical: 4 };
        const storyPriority = priorityOrder[story.priority] || 1;
        const thresholdPriority = priorityOrder[this.config.guardrails.priorityThreshold] || 1;

        if (storyPriority < thresholdPriority) {
          console.log(`⏭️ Story "${story.title}" priority (${story.priority}) below threshold`);
          return false;
        }
      }

      return true;
    });
  }

  /**
   * Get count of currently processing stories for a project
   */
  async getCurrentlyProcessingStories(projectId) {
    return new Promise((resolve, reject) => {
      const query = `SELECT COUNT(*) as count FROM user_stories
                     WHERE project_id = ?
                     AND implementation_status = 'processing'`;

      db.get(query, [projectId], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row?.count || 0);
        }
      });
    });
  }

  /**
   * Enhanced method to get active projects with AI processing status
   */
  async getActiveProjects() {
    return new Promise((resolve, reject) => {
      let query = `SELECT * FROM projects
                   WHERE ai_enabled = 1
                   AND ai_processing_enabled = 1`;

      if (this.config.guardrails.requireActiveProject) {
        query += ` AND status IN ('active', 'in_progress')`;
      }

      query += ` ORDER BY created_at ASC`;

      db.all(query, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows || []);
        }
      });
    });
  }

  /**
   * Enhanced method to get active sprint with AI processing status
   */
  async getActiveSprint(projectId) {
    return new Promise((resolve, reject) => {
      const now = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format

      const query = `SELECT * FROM sprints
                     WHERE project_id = ?
                     AND status = 'active'
                     AND ai_processing_enabled = 1
                     AND start_date <= ?
                     AND end_date >= ?
                     ORDER BY start_date DESC
                     LIMIT 1`;

      db.get(query, [projectId, now, now], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row || null);
        }
      });
    });
  }

  /**
   * Enhanced method to get pending work with priority filtering
   */
  async getPendingWork(projectId, sprintId = null) {
    return new Promise((resolve, reject) => {
      let query = `SELECT * FROM user_stories
                   WHERE project_id = ?
                   AND implementation_status IN ('not_started', 'modified_after_implementation', 'validation_failed')
                   AND status IN ('backlog', 'in_progress')`;

      const params = [projectId];

      if (sprintId && this.config.guardrails.requireActiveSprint) {
        query += ` AND sprint_id = ?`;
        params.push(sprintId);
      }

      query += ` ORDER BY
                 CASE priority
                   WHEN 'critical' THEN 4
                   WHEN 'high' THEN 3
                   WHEN 'medium' THEN 2
                   WHEN 'low' THEN 1
                   ELSE 0
                 END DESC,
                 created_at ASC`;

      db.all(query, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows || []);
        }
      });
    });
  }

  /**
   * Get stories that need validation against acceptance criteria
   */
  async getStoriesNeedingValidation(projectId, sprintId = null) {
    return new Promise((resolve, reject) => {
      let query = `SELECT * FROM user_stories
                   WHERE project_id = ?
                   AND implementation_status = 'implemented'
                   AND status IN ('backlog', 'in_progress')`;

      const params = [projectId];

      if (sprintId && this.config.guardrails.requireActiveSprint) {
        query += ` AND sprint_id = ?`;
        params.push(sprintId);
      }

      query += ` ORDER BY
                 CASE priority
                   WHEN 'critical' THEN 4
                   WHEN 'high' THEN 3
                   WHEN 'medium' THEN 2
                   WHEN 'low' THEN 1
                   ELSE 0
                 END DESC,
                 last_modified_at ASC`;

      db.all(query, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows || []);
        }
      });
    });
  }

  /**
   * Update the implementation status of a user story
   */
  async updateStoryImplementationStatus(storyId, status) {
    return new Promise((resolve, reject) => {
      const query = `UPDATE user_stories
                     SET implementation_status = ?,
                         updated_at = CURRENT_TIMESTAMP
                     WHERE id = ?`;

      db.run(query, [status, storyId], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes);
        }
      });
    });
  }

  /**
   * Utility function for delays
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

module.exports = { AIProcessingScheduler };
