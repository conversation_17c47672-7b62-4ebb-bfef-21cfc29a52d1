import React, { useState, useEffect } from 'react';
import ConfirmationModal from './ConfirmationModal';
import './AISchedulerControl.css';

const AISchedulerControl = () => {
  const [schedulerStatus, setSchedulerStatus] = useState(null);
  const [loading, setLoading] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [modalMessage, setModalMessage] = useState('');
  const [config, setConfig] = useState({
    tickRate: 30000,
    maxConcurrentProjects: 3,
    workingHours: {
      enabled: false,
      start: '09:00',
      end: '17:00'
    },
    guardrails: {
      requireActiveProject: true,
      requireActiveSprint: true,
      maxStoriesPerTick: 2,
      cooldownBetweenStories: 5000,
      minSprintDaysRemaining: 1,
      maxConcurrentStoriesPerProject: 3,
      priorityThreshold: 'medium',
      blockOnCriticalIssues: true,
      requireMinimumSprintCapacity: 0.2
    }
  });
  const [activeProjects, setActiveProjects] = useState([]);
  const [activeSprints, setActiveSprints] = useState([]);

  useEffect(() => {
    fetchSchedulerStatus();
    fetchActiveProjects();
    fetchActiveSprints();
    
    // Refresh status every 10 seconds
    const interval = setInterval(fetchSchedulerStatus, 10000);
    return () => clearInterval(interval);
  }, []);

  const fetchSchedulerStatus = async () => {
    try {
      const response = await fetch('/api/ai/scheduler/status');
      const data = await response.json();
      setSchedulerStatus(data);
      
      if (data.config) {
        setConfig(data.config);
      }
    } catch (error) {
      console.error('Error fetching scheduler status:', error);
    }
  };

  const fetchActiveProjects = async () => {
    try {
      const response = await fetch('/api/projects');
      const data = await response.json();
      setActiveProjects(data.projects.filter(p => p.status === 'active' && p.ai_enabled));
    } catch (error) {
      console.error('Error fetching active projects:', error);
    }
  };

  const fetchActiveSprints = async () => {
    try {
      const response = await fetch('/api/sprints/active');
      const data = await response.json();
      setActiveSprints(data.sprints || []);
    } catch (error) {
      console.error('Error fetching active sprints:', error);
    }
  };

  const startScheduler = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/ai/scheduler/start', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ config })
      });
      
      if (response.ok) {
        await fetchSchedulerStatus();
        setModalMessage('AI Scheduler started successfully!');
        setShowSuccessModal(true);
      } else {
        const error = await response.json();
        setModalMessage(`Failed to start scheduler: ${error.error}`);
        setShowErrorModal(true);
      }
    } catch (error) {
      console.error('Error starting scheduler:', error);
      setModalMessage('Error starting scheduler');
      setShowErrorModal(true);
    } finally {
      setLoading(false);
    }
  };

  const stopScheduler = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/ai/scheduler/stop', {
        method: 'POST'
      });
      
      if (response.ok) {
        await fetchSchedulerStatus();
        setModalMessage('AI Scheduler stopped successfully!');
        setShowSuccessModal(true);
      } else {
        const error = await response.json();
        setModalMessage(`Failed to stop scheduler: ${error.error}`);
        setShowErrorModal(true);
      }
    } catch (error) {
      console.error('Error stopping scheduler:', error);
      setModalMessage('Error stopping scheduler');
      setShowErrorModal(true);
    } finally {
      setLoading(false);
    }
  };

  const updateConfig = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/ai/scheduler/config', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ config })
      });
      
      if (response.ok) {
        await fetchSchedulerStatus();
        setModalMessage('Configuration updated successfully!');
        setShowSuccessModal(true);
      } else {
        const error = await response.json();
        setModalMessage(`Failed to update config: ${error.error}`);
        setShowErrorModal(true);
      }
    } catch (error) {
      console.error('Error updating config:', error);
      setModalMessage('Error updating configuration');
      setShowErrorModal(true);
    } finally {
      setLoading(false);
    }
  };

  const formatUptime = (ms) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  };

  const getStatusColor = (isRunning) => {
    return isRunning ? '#4CAF50' : '#f44336';
  };

  if (!schedulerStatus) {
    return <div className="ai-scheduler-loading">Loading AI Scheduler status...</div>;
  }

  return (
    <div className="ai-scheduler-control">
      <div className="scheduler-header">
        <h2>AI Processing Scheduler</h2>
        <div className="scheduler-status">
          <div 
            className="status-indicator"
            style={{ backgroundColor: getStatusColor(schedulerStatus.isRunning) }}
          ></div>
          <span className="status-text">
            {schedulerStatus.isRunning ? 'Running' : 'Stopped'}
          </span>
        </div>
      </div>

      <div className="scheduler-content">
        {/* Statistics Section - Full Width & Stylish */}
        <div className="stats-section-full">
          <h3>AI Scheduler Statistics</h3>
          <div className="stats-grid">
            <div className="stat-card-modern">
              <div className="stat-icon">
                <div className="icon-circle blue">
                  <span>⚡</span>
                </div>
              </div>
              <div className="stat-content">
                <div className="stat-value-large">{schedulerStatus.stats.totalTicks}</div>
                <div className="stat-label-modern">Total Ticks</div>
              </div>
            </div>

            <div className="stat-card-modern">
              <div className="stat-icon">
                <div className="icon-circle green">
                  <span>✓</span>
                </div>
              </div>
              <div className="stat-content">
                <div className="stat-value-large">{schedulerStatus.stats.storiesProcessed}</div>
                <div className="stat-label-modern">Stories Processed</div>
              </div>
            </div>

            <div className="stat-card-modern">
              <div className="stat-icon">
                <div className="icon-circle red">
                  <span>⚠</span>
                </div>
              </div>
              <div className="stat-content">
                <div className="stat-value-large">{schedulerStatus.stats.errors}</div>
                <div className="stat-label-modern">Errors</div>
              </div>
            </div>

            <div className="stat-card-modern">
              <div className="stat-icon">
                <div className="icon-circle orange">
                  <span>⏸</span>
                </div>
              </div>
              <div className="stat-content">
                <div className="stat-value-large">{schedulerStatus.stats.skippedTicks}</div>
                <div className="stat-label-modern">Skipped Ticks</div>
              </div>
            </div>

            {schedulerStatus.stats.lastActivity && (
              <div className="stat-card-modern last-activity">
                <div className="stat-icon">
                  <div className="icon-circle purple">
                    <span>🕒</span>
                  </div>
                </div>
                <div className="stat-content">
                  <div className="stat-value-time">
                    {new Date(schedulerStatus.stats.lastActivity).toLocaleString()}
                  </div>
                  <div className="stat-label-modern">Last Activity</div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Active Projects - Horizontal Section */}
        <div className="horizontal-section">
          <h3>Active Projects ({activeProjects.length})</h3>
          <div className="horizontal-content">
            {activeProjects.length > 0 ? (
              activeProjects.map(project => (
                <div key={project.id} className="horizontal-item">
                  <span className="item-name">{project.name}</span>
                  <span className={`item-status ${project.ai_processing_enabled ? 'enabled' : 'disabled'}`}>
                    {project.ai_processing_enabled ? 'AI Enabled' : 'AI Disabled'}
                  </span>
                </div>
              ))
            ) : (
              <div className="empty-state">No active projects with AI enabled</div>
            )}
          </div>
        </div>

        {/* Active Sprints - Horizontal Section */}
        <div className="horizontal-section">
          <h3>Active Sprints ({activeSprints.length})</h3>
          <div className="horizontal-content">
            {activeSprints.length > 0 ? (
              activeSprints.map(sprint => (
                <div key={sprint.id} className="horizontal-item">
                  <span className="item-name">{sprint.name}</span>
                  <span className="item-project">({sprint.project_name})</span>
                  <span className="item-dates">
                    {sprint.start_date} - {sprint.end_date}
                  </span>
                </div>
              ))
            ) : (
              <div className="empty-state">No active sprints</div>
            )}
          </div>
        </div>
      </div>

      <div className="scheduler-controls">
        <div className="control-buttons">
          {schedulerStatus.isRunning ? (
            <button 
              className="btn btn-danger"
              onClick={stopScheduler}
              disabled={loading}
            >
              {loading ? 'Stopping...' : 'Stop Scheduler'}
            </button>
          ) : (
            <button 
              className="btn btn-success"
              onClick={startScheduler}
              disabled={loading}
            >
              {loading ? 'Starting...' : 'Start Scheduler'}
            </button>
          )}
          
          <button 
            className="btn btn-primary"
            onClick={updateConfig}
            disabled={loading || schedulerStatus.isRunning}
          >
            {loading ? 'Updating...' : 'Update Config'}
          </button>
        </div>

        <div className="config-section">
          <h3>Configuration</h3>
          
          <div className="config-group">
            <label>Tick Rate (ms):</label>
            <input
              type="number"
              value={config.tickRate}
              onChange={(e) => setConfig({...config, tickRate: parseInt(e.target.value)})}
              disabled={schedulerStatus.isRunning}
              min="5000"
              max="300000"
            />
          </div>

          <div className="config-group">
            <label>Max Concurrent Projects:</label>
            <input
              type="number"
              value={config.maxConcurrentProjects}
              onChange={(e) => setConfig({...config, maxConcurrentProjects: parseInt(e.target.value)})}
              disabled={schedulerStatus.isRunning}
              min="1"
              max="10"
            />
          </div>

          <div className="config-group">
            <label>Max Stories Per Tick:</label>
            <input
              type="number"
              value={config.guardrails.maxStoriesPerTick}
              onChange={(e) => setConfig({
                ...config, 
                guardrails: {...config.guardrails, maxStoriesPerTick: parseInt(e.target.value)}
              })}
              disabled={schedulerStatus.isRunning}
              min="1"
              max="10"
            />
          </div>

          <div className="config-group">
            <label>Priority Threshold:</label>
            <select
              value={config.guardrails.priorityThreshold}
              onChange={(e) => setConfig({
                ...config, 
                guardrails: {...config.guardrails, priorityThreshold: e.target.value}
              })}
              disabled={schedulerStatus.isRunning}
            >
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="critical">Critical</option>
            </select>
          </div>

          <div className="config-group">
            <label>
              <input
                type="checkbox"
                checked={config.guardrails.requireActiveSprint}
                onChange={(e) => setConfig({
                  ...config, 
                  guardrails: {...config.guardrails, requireActiveSprint: e.target.checked}
                })}
                disabled={schedulerStatus.isRunning}
              />
              Require Active Sprint
            </label>
          </div>

          <div className="config-group">
            <label>
              <input
                type="checkbox"
                checked={config.guardrails.blockOnCriticalIssues}
                onChange={(e) => setConfig({
                  ...config, 
                  guardrails: {...config.guardrails, blockOnCriticalIssues: e.target.checked}
                })}
                disabled={schedulerStatus.isRunning}
              />
              Block on Critical Issues
            </label>
          </div>
        </div>
      </div>

      {/* Success Modal */}
      <ConfirmationModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        onConfirm={() => setShowSuccessModal(false)}
        title="Success"
        message={modalMessage}
        confirmText="OK"
        cancelText=""
        type="default"
      />

      {/* Error Modal */}
      <ConfirmationModal
        isOpen={showErrorModal}
        onClose={() => setShowErrorModal(false)}
        onConfirm={() => setShowErrorModal(false)}
        title="Error"
        message={modalMessage}
        confirmText="OK"
        cancelText=""
        type="danger"
      />
    </div>
  );
};

export default AISchedulerControl;
