import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  ArrowLeft,
  FileText,
  Calendar,
  Search,
  Filter,
  Download,
  Eye,
  Clock,
  BarChart3,
  Users,
  Target,
  Trash2
} from 'lucide-react';
import ProjectSidebar from './ProjectSidebar';
import DashboardSidebar from './DashboardSidebar';
import MarkdownViewer from './MarkdownViewer';
import ConfirmationModal from './ConfirmationModal';
import './Reports.css';

const Reports = ({ isDashboard = false }) => {
  const { id } = useParams(); // Project ID if viewing project reports
  const navigate = useNavigate();
  
  const [reports, setReports] = useState([]);
  const [filteredReports, setFilteredReports] = useState([]);
  const [selectedReport, setSelectedReport] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [dateRange, setDateRange] = useState('all');
  const [project, setProject] = useState(null);
  const [stats, setStats] = useState({});
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [reportToDelete, setReportToDelete] = useState(null);

  // Fetch reports data
  useEffect(() => {
    const fetchReports = async () => {
      try {
        setLoading(true);
        
        if (isDashboard) {
          // Fetch all reports across all projects
          const response = await fetch('/api/reports');
          const data = await response.json();
          setReports(data.reports || []);
        } else if (id) {
          // Fetch project-specific reports
          const [reportsResponse, projectResponse] = await Promise.all([
            fetch(`/api/projects/${id}/reports`),
            fetch(`/api/projects/${id}`)
          ]);
          
          const reportsData = await reportsResponse.json();
          const projectData = await projectResponse.json();
          
          setReports(reportsData.reports || []);
          setProject(projectData);

          // Calculate basic stats for project
          if (projectData) {
            setStats({
              total: reportsData.reports?.length || 0,
              backlog: 0,
              inProgress: 0,
              completed: 0
            });
          }
        }

        // Calculate dashboard stats if needed
        if (isDashboard) {
          setStats({
            total: reports.length,
            projects: new Set(reports.map(r => r.project_id)).size
          });
        }
      } catch (err) {
        setError('Failed to load reports');
        console.error('Error fetching reports:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchReports();
  }, [id, isDashboard]);

  // Filter and search reports
  useEffect(() => {
    let filtered = reports;

    // Filter by type
    if (filterType !== 'all') {
      filtered = filtered.filter(report => report.type === filterType);
    }

    // Filter by date range
    if (dateRange !== 'all') {
      const now = new Date();
      let cutoffDate;

      switch (dateRange) {
        case 'today':
          cutoffDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          break;
        case 'week':
          cutoffDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          cutoffDate = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
          break;
        case 'quarter':
          cutoffDate = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate());
          break;
        default:
          cutoffDate = null;
      }

      if (cutoffDate) {
        filtered = filtered.filter(report =>
          new Date(report.generated_at) >= cutoffDate
        );
      }
    }

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(report =>
        report.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        report.content.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredReports(filtered);
  }, [reports, filterType, dateRange, searchTerm]);

  const handleReportSelect = (report) => {
    setSelectedReport(report);
  };

  const handleDownloadReport = (report) => {
    const blob = new Blob([report.content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${report.title.replace(/\s+/g, '-')}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleDeleteReport = (report) => {
    setReportToDelete(report);
    setShowDeleteModal(true);
  };

  const confirmDeleteReport = async () => {
    if (!reportToDelete) return;

    try {
      const endpoint = isDashboard
        ? `/api/reports/${reportToDelete.id}`
        : `/api/projects/${id}/reports/${reportToDelete.id}`;

      const response = await fetch(endpoint, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete report');
      }

      // Remove the report from the local state
      setReports(prevReports => prevReports.filter(r => r.id !== reportToDelete.id));

      // Clear selected report if it was the one being deleted
      if (selectedReport?.id === reportToDelete.id) {
        setSelectedReport(null);
      }

      // Close modal and reset state
      setShowDeleteModal(false);
      setReportToDelete(null);
    } catch (error) {
      console.error('Error deleting report:', error);
      setError('Failed to delete report. Please try again.');
      setShowDeleteModal(false);
      setReportToDelete(null);
    }
  };

  const cancelDeleteReport = () => {
    setShowDeleteModal(false);
    setReportToDelete(null);
  };

  const getReportTypeIcon = (type) => {
    switch (type) {
      case 'progress': return <FileText size={16} />;
      case 'sprint': return <Target size={16} />;
      case 'analytics': return <BarChart3 size={16} />;
      default: return <FileText size={16} />;
    }
  };

  const getReportTypeColor = (type) => {
    switch (type) {
      case 'progress': return '#3b82f6';
      case 'sprint': return '#10b981';
      case 'analytics': return '#f59e0b';
      default: return '#6b7280';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Loading reports...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="error-container">
        <h2>Error Loading Reports</h2>
        <p>{error}</p>
        <button onClick={() => navigate(isDashboard ? '/' : `/projects/${id}`)} className="btn-primary">
          <ArrowLeft size={20} />
          Go Back
        </button>
      </div>
    );
  }

  return (
    <div className="project-detail-modern">
      {/* Sidebar */}
      {isDashboard ? (
        <DashboardSidebar
          activeView="reports"
          onViewChange={(view) => {
            if (view === 'reports') {
              // Already on reports page
              return;
            } else {
              // Navigate to dashboard with the selected view
              navigate('/', { state: { activeView: view } });
            }
          }}
          stats={stats}
        />
      ) : (
        <ProjectSidebar
          activeView="reports"
          onViewChange={(view) => {
            if (view === 'reports') {
              // Already on reports page
              return;
            } else {
              // Navigate back to project with the selected view
              navigate(`/projects/${id}`, { state: { activeView: view } });
            }
          }}
          project={project}
          stats={stats}
        />
      )}

      {/* Main Content */}
      <div className="main-content">
        <div className="reports-container">
          {/* Header */}
          <div className="reports-header">
            <div className="header-content">
              <h1>
                {isDashboard ? 'All Reports' : `${project?.name} Reports`}
                {filteredReports.length > 0 && (
                  <span className="reports-count">({filteredReports.length})</span>
                )}
              </h1>
              <p>
                {isDashboard
                  ? 'View reports from all your projects'
                  : 'AI-generated progress reports and analytics'
                }
              </p>
            </div>
            
            {!isDashboard && (
              <button 
                onClick={() => navigate(`/projects/${id}`)} 
                className="back-button"
              >
                <ArrowLeft size={20} />
                Back to Project
              </button>
            )}
          </div>

          {/* Filters and Search */}
          <div className="reports-controls">
            <div className="search-container">
              <Search size={20} className="search-icon" />
              <input
                type="text"
                placeholder="Search reports..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="search-input"
              />
            </div>

            <div className="filter-container">
              <Filter size={20} className="filter-icon" />
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="filter-select"
              >
                <option value="all">All Types</option>
                <option value="progress">Progress Reports</option>
                <option value="sprint">Sprint Reports</option>
                <option value="analytics">Analytics</option>
              </select>
            </div>

            <div className="filter-container">
              <Calendar size={20} className="filter-icon" />
              <select
                value={dateRange}
                onChange={(e) => setDateRange(e.target.value)}
                className="filter-select"
              >
                <option value="all">All Time</option>
                <option value="today">Today</option>
                <option value="week">Last Week</option>
                <option value="month">Last Month</option>
                <option value="quarter">Last Quarter</option>
              </select>
            </div>
          </div>

          {/* Reports List and Viewer */}
          <div className="reports-content">
            {/* Reports List */}
            <div className="reports-list">
              {filteredReports.length === 0 ? (
                <div className="empty-state">
                  <FileText size={48} />
                  <h3>No Reports Found</h3>
                  <p>
                    {searchTerm || filterType !== 'all' 
                      ? 'Try adjusting your search or filter criteria'
                      : 'Reports will appear here as they are generated by the AI team'
                    }
                  </p>
                </div>
              ) : (
                filteredReports.map((report) => (
                  <div
                    key={report.id}
                    className={`report-item ${selectedReport?.id === report.id ? 'selected' : ''}`}
                    onClick={() => handleReportSelect(report)}
                  >
                    <div className="report-header">
                      <div className="report-type" style={{ color: getReportTypeColor(report.type) }}>
                        {getReportTypeIcon(report.type)}
                        <span>{report.type}</span>
                      </div>
                      <div className="report-actions">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDownloadReport(report);
                          }}
                          className="action-button"
                          title="Download Report"
                        >
                          <Download size={16} />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteReport(report);
                          }}
                          className="action-button delete-button"
                          title="Delete Report"
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </div>
                    
                    <h3 className="report-title">{report.title}</h3>
                    
                    <div className="report-meta">
                      <div className="meta-item">
                        <Clock size={14} />
                        <span>{formatDate(report.generated_at)}</span>
                      </div>
                      {!isDashboard && report.project_name && (
                        <div className="meta-item">
                          <Users size={14} />
                          <span>{report.project_name}</span>
                        </div>
                      )}
                    </div>
                    
                    <p className="report-summary">{report.summary}</p>
                  </div>
                ))
              )}
            </div>

            {/* Report Viewer */}
            {selectedReport && (
              <div className="report-viewer">
                <div className="viewer-header">
                  <h2>{selectedReport.title}</h2>
                  <button
                    onClick={() => handleDownloadReport(selectedReport)}
                    className="download-button"
                  >
                    <Download size={20} />
                    Download
                  </button>
                </div>
                
                <div className="report-content">
                  <MarkdownViewer content={selectedReport.content} />
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={showDeleteModal}
        onClose={cancelDeleteReport}
        onConfirm={confirmDeleteReport}
        title="Delete Report"
        message={`Are you sure you want to delete "${reportToDelete?.title}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        type="danger"
      />
    </div>
  );
};

export default Reports;
