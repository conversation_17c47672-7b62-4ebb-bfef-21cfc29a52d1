const { nanoid } = require('nanoid');
const db = require('./database');

class AICommentManager {
  constructor() {
    this.agentNames = {
      PRODUCT_OWNER: 'ProductOwnerAgent',
      SCRUM_MASTER: 'ScrumMasterAgent', 
      LEAD_DEVELOPER: 'LeadDeveloperAgent',
      PROFESSIONAL_WORKER: 'ProfessionalWorkerAgent'
    };
  }

  /**
   * Read all comments for an entity before starting work
   * @param {string} projectId - Project ID
   * @param {string} entityType - 'user_story', 'task', 'sprint', or 'project'
   * @param {string} entityId - Entity ID
   * @param {boolean} includeInternal - Include internal AI comments
   * @returns {Promise<Array>} Array of comments
   */
  async readComments(projectId, entityType, entityId, includeInternal = true) {
    return new Promise((resolve, reject) => {
      let query = `SELECT c.*, 
                          CASE WHEN c.parent_comment_id IS NOT NULL THEN 
                              (SELECT comment_text FROM comments WHERE id = c.parent_comment_id) 
                          ELSE NULL END as parent_comment_text
                   FROM comments c 
                   WHERE c.project_id = ? AND c.entity_type = ? AND c.entity_id = ?`;
      
      const params = [projectId, entityType, entityId];
      
      if (!includeInternal) {
        query += ' AND c.is_internal = FALSE';
      }
      
      query += ' ORDER BY c.created_at ASC';
      
      db.all(query, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          const comments = rows.map(row => ({
            ...row,
            metadata: JSON.parse(row.metadata || '{}')
          }));
          resolve(comments);
        }
      });
    });
  }

  /**
   * Add a comment from an AI agent
   * @param {string} projectId - Project ID
   * @param {string} entityType - 'user_story', 'task', 'sprint', or 'project'
   * @param {string} entityId - Entity ID
   * @param {string} agentName - Name of the AI agent
   * @param {string} commentText - Comment content
   * @param {string} commentType - Type of comment
   * @param {Object} metadata - Additional metadata
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Created comment
   */
  async addComment(projectId, entityType, entityId, agentName, commentText, commentType = 'progress', metadata = {}, options = {}) {
    const {
      parentCommentId = null,
      isInternal = true,
      priority = 'normal',
      mentions = []
    } = options;

    return new Promise((resolve, reject) => {
      const commentId = nanoid();
      const query = `INSERT INTO comments (
        id, entity_type, entity_id, project_id, author_type, author_name,
        comment_text, comment_type, metadata, parent_comment_id, is_internal, priority
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;

      db.run(query, [
        commentId, entityType, entityId, projectId, 'ai_agent', agentName,
        commentText, commentType, JSON.stringify(metadata), parentCommentId, isInternal, priority
      ], async (err) => {
        if (err) {
          reject(err);
          return;
        }

        // Add mentions if specified
        if (mentions.length > 0) {
          await this.addMentions(commentId, mentions);
        }

        const createdComment = {
          id: commentId,
          entity_type: entityType,
          entity_id: entityId,
          project_id: projectId,
          author_type: 'ai_agent',
          author_name: agentName,
          comment_text: commentText,
          comment_type: commentType,
          metadata,
          parent_comment_id: parentCommentId,
          is_internal: isInternal,
          priority,
          status: 'active',
          created_at: new Date().toISOString()
        };

        resolve(createdComment);
      });
    });
  }

  /**
   * Add mentions for other AI agents
   * @param {string} commentId - Comment ID
   * @param {Array<string>} agentNames - Array of agent names to mention
   */
  async addMentions(commentId, agentNames) {
    const mentionPromises = agentNames.map(agentName => {
      return new Promise((resolve, reject) => {
        const mentionId = nanoid();
        const query = `INSERT INTO comment_mentions (id, comment_id, mentioned_agent) VALUES (?, ?, ?)`;
        
        db.run(query, [mentionId, commentId, agentName], (err) => {
          if (err) {
            reject(err);
          } else {
            resolve(mentionId);
          }
        });
      });
    });

    return Promise.all(mentionPromises);
  }

  /**
   * Get unread mentions for an AI agent
   * @param {string} projectId - Project ID
   * @param {string} agentName - Agent name
   * @returns {Promise<Array>} Array of unread mentions
   */
  async getUnreadMentions(projectId, agentName) {
    return new Promise((resolve, reject) => {
      const query = `SELECT cm.*, c.comment_text, c.entity_type, c.entity_id, c.author_name
                     FROM comment_mentions cm
                     JOIN comments c ON cm.comment_id = c.id
                     WHERE c.project_id = ? AND cm.mentioned_agent = ? AND cm.is_read = FALSE
                     ORDER BY cm.created_at DESC`;
      
      db.all(query, [projectId, agentName], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  /**
   * Mark mentions as read
   * @param {Array<string>} mentionIds - Array of mention IDs
   * @param {string} agentName - Agent name
   */
  async markMentionsRead(mentionIds, agentName) {
    if (!mentionIds || mentionIds.length === 0) return;

    return new Promise((resolve, reject) => {
      const placeholders = mentionIds.map(() => '?').join(',');
      const query = `UPDATE comment_mentions 
                     SET is_read = TRUE 
                     WHERE id IN (${placeholders}) 
                     AND mentioned_agent = ?`;
      
      db.run(query, [...mentionIds, agentName], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve(this.changes);
        }
      });
    });
  }

  /**
   * Get comments summary for AI context
   * @param {string} projectId - Project ID
   * @param {string} entityType - Entity type
   * @param {string} entityId - Entity ID
   * @returns {Promise<string>} Formatted summary of comments
   */
  async getCommentsSummary(projectId, entityType, entityId) {
    const comments = await this.readComments(projectId, entityType, entityId, true);
    
    if (comments.length === 0) {
      return "No previous comments or progress updates found.";
    }

    let summary = `Previous Comments and Progress (${comments.length} total):\n\n`;
    
    comments.forEach((comment, index) => {
      const timestamp = new Date(comment.created_at).toLocaleString();
      summary += `${index + 1}. [${comment.author_name}] ${timestamp} - ${comment.comment_type.toUpperCase()}\n`;
      summary += `   ${comment.comment_text}\n`;
      
      if (comment.metadata && Object.keys(comment.metadata).length > 0) {
        summary += `   Metadata: ${JSON.stringify(comment.metadata)}\n`;
      }
      
      if (comment.priority !== 'normal') {
        summary += `   Priority: ${comment.priority}\n`;
      }
      
      summary += '\n';
    });

    return summary;
  }

  /**
   * Check for blockers or critical issues in comments
   * @param {string} projectId - Project ID
   * @param {string} entityType - Entity type
   * @param {string} entityId - Entity ID
   * @returns {Promise<Array>} Array of blocker comments
   */
  async getBlockers(projectId, entityType, entityId) {
    const comments = await this.readComments(projectId, entityType, entityId, true);
    
    return comments.filter(comment => 
      comment.comment_type === 'blocker' || 
      comment.priority === 'critical' ||
      comment.status === 'active'
    );
  }

  /**
   * Log AI agent decision with reasoning
   * @param {string} projectId - Project ID
   * @param {string} entityType - Entity type
   * @param {string} entityId - Entity ID
   * @param {string} agentName - Agent name
   * @param {string} decision - Decision made
   * @param {string} reasoning - Reasoning behind decision
   * @param {Object} metadata - Additional context
   */
  async logDecision(projectId, entityType, entityId, agentName, decision, reasoning, metadata = {}) {
    const commentText = `DECISION: ${decision}\n\nREASONING: ${reasoning}`;
    
    return this.addComment(
      projectId, 
      entityType, 
      entityId, 
      agentName, 
      commentText, 
      'decision',
      { decision, reasoning, ...metadata },
      { isInternal: true, priority: 'normal' }
    );
  }
}

module.exports = { AICommentManager };
