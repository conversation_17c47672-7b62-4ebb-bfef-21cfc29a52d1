# Hourly AI Validation System

This document describes the refactored AI system that automatically checks for new work every hour and validates implemented user stories against their acceptance criteria.

## Overview

The AI system now operates on an hourly cycle to:
1. **Check for new or updated user stories** that need implementation
2. **Validate implemented stories** against their acceptance criteria
3. **Ensure project code meets requirements** before marking stories as complete
4. **Only process active projects** with non-planning sprints

## Key Features

### 🕐 Hourly Processing
- **Tick Rate**: Changed from 30 seconds to 1 hour (3600000ms)
- **Batch Processing**: Processes multiple stories per cycle for efficiency
- **Smart Scheduling**: Prioritizes validation over new implementation

### 🔍 Acceptance Criteria Validation
- **AI-Powered Analysis**: Uses advanced AI to evaluate code against acceptance criteria
- **Scoring System**: Provides 0-100 scores with configurable pass threshold (default: 70)
- **Detailed Feedback**: Generates specific recommendations for improvements
- **Database Tracking**: Stores validation scores and feedback for audit trails

### 📋 Enhanced Story Lifecycle
```
not_started → implemented → validated
                    ↓
              validation_failed → (back to implementation)
```

### 🛡️ Guardrails & Safety
- **Active Projects Only**: Only processes projects with status 'active' or 'in_progress'
- **Active Sprints Only**: Excludes 'planning' sprints from processing
- **Priority Filtering**: Processes medium+ priority stories by default
- **Concurrent Limits**: Prevents system overload with configurable limits

## Configuration

### Default Hourly Configuration
```javascript
{
  tickRate: 3600000, // 1 hour
  guardrails: {
    enableAcceptanceCriteriaValidation: true,
    validationScoreThreshold: 70,
    maxStoriesPerTick: 5,
    maxConcurrentStoriesPerProject: 5,
    requireActiveProject: true,
    requireActiveSprint: true
  }
}
```

### Environment Variables
- `AI_VALIDATION_ENABLED`: Enable/disable validation (default: true)
- `AI_VALIDATION_THRESHOLD`: Minimum score to pass (default: 70)
- `AI_TICK_RATE`: Override default tick rate in milliseconds

## API Endpoints

### Scheduler Control
- `GET /api/ai/scheduler/status` - Get current scheduler status
- `POST /api/ai/scheduler/start` - Start scheduler with optional config
- `POST /api/ai/scheduler/stop` - Stop scheduler
- `PATCH /api/ai/scheduler/config` - Update configuration

### Example Status Response
```json
{
  "isRunning": true,
  "config": {
    "tickRate": 3600000,
    "guardrails": {
      "enableAcceptanceCriteriaValidation": true,
      "validationScoreThreshold": 70
    }
  },
  "stats": {
    "totalTicks": 24,
    "storiesProcessed": 15,
    "storiesValidated": 12,
    "validationsPassed": 10,
    "validationsFailed": 2
  }
}
```

## Database Schema Updates

### New User Story Fields
```sql
ALTER TABLE user_stories ADD COLUMN validation_score INTEGER DEFAULT 0;
ALTER TABLE user_stories ADD COLUMN validation_feedback TEXT;
```

### New Implementation Status Values
- `not_started` - Story hasn't been implemented yet
- `implemented` - Story has been implemented but not validated
- `validated` - Story passed acceptance criteria validation
- `validation_failed` - Story failed validation and needs rework
- `modified_after_implementation` - Story was changed after implementation

## Validation Process

### 1. Code Discovery
- Scans project workspace for relevant code files
- Filters by file extensions (.js, .jsx, .ts, .tsx, .py, etc.)
- Identifies files modified recently or containing story keywords

### 2. AI Analysis
- Compares implemented code against acceptance criteria
- Evaluates functionality completeness
- Checks for obvious bugs or issues
- Assesses code quality and best practices

### 3. Scoring & Feedback
- Generates 0-100 score based on criteria fulfillment
- Provides detailed feedback on what passes/fails
- Offers specific recommendations for improvement
- Logs results as comments and in database

### 4. Status Updates
- Updates story implementation_status based on results
- Stores validation score and feedback
- Triggers notifications for failed validations

## Testing

### Run Validation Tests
```bash
node backend/test-hourly-validation.js
```

### Test Coverage
- AcceptanceCriteriaValidator functionality
- Scheduler configuration and processing
- Database operations and migrations
- Story lifecycle management
- Error handling and recovery

## Monitoring & Observability

### Logs
- Hourly tick processing logs
- Validation results and scores
- Error tracking and debugging
- Performance metrics

### Comments System
- All validation results logged as story comments
- Detailed feedback and recommendations
- Error tracking with priority levels
- Audit trail for compliance

### Statistics Tracking
- Stories processed per hour
- Validation pass/fail rates
- Average validation scores
- Processing time metrics

## Best Practices

### For Development Teams
1. **Write Clear Acceptance Criteria**: Specific, testable criteria improve validation accuracy
2. **Regular Code Reviews**: Human review complements AI validation
3. **Monitor Validation Scores**: Track trends to improve development quality
4. **Address Failed Validations Promptly**: Don't let validation debt accumulate

### For Project Managers
1. **Set Appropriate Thresholds**: Balance quality with development velocity
2. **Review Validation Reports**: Use insights to improve story writing
3. **Monitor Sprint Health**: Track validation metrics across sprints
4. **Adjust Priorities**: Use validation feedback to prioritize improvements

## Troubleshooting

### Common Issues

#### Validation Always Fails
- Check acceptance criteria clarity and specificity
- Verify workspace path is correct
- Ensure code files are in expected locations
- Review validation threshold settings

#### No Stories Being Processed
- Verify project status is 'active'
- Check sprint status is not 'planning'
- Confirm AI processing is enabled
- Review priority threshold settings

#### Performance Issues
- Reduce maxStoriesPerTick if processing is slow
- Increase cooldown between stories
- Monitor system resources during processing
- Consider adjusting tick rate for large projects

### Debug Mode
Enable detailed logging by setting environment variable:
```bash
DEBUG_AI_VALIDATION=true
```

## Future Enhancements

### Planned Features
- **Custom Validation Rules**: Project-specific validation criteria
- **Integration Testing**: Automated testing of implemented features
- **Performance Benchmarks**: Code performance validation
- **Security Scanning**: Automated security vulnerability detection
- **Documentation Generation**: Auto-generate docs from validated code

### Metrics Dashboard
- Real-time validation statistics
- Historical trend analysis
- Project comparison views
- Team performance insights

## Support

For issues or questions about the hourly validation system:
1. Check the troubleshooting section above
2. Review system logs for error details
3. Test with the provided test scripts
4. Contact the development team with specific error messages

---

*Last updated: 2025-09-15*
*Version: 1.0.0*
