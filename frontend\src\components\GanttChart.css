.gantt-chart {
  background: #1a1a1a;
  border-radius: 8px;
  padding: 1.25rem;
  border: 1px solid #2d2d2d;
  margin-bottom: 2rem;
  color: #e5e5e5;
}

.gantt-controls {
  margin-bottom: 1rem;
  display: flex;
  justify-content: flex-end;
}

.view-mode-select {
  background: #2d2d2d;
  color: #e5e5e5;
  border: 1px solid #374151;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 14px;
  cursor: pointer;
  transition: border-color 0.15s ease;
}

.view-mode-select:hover {
  border-color: #3b82f6;
}

.view-mode-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.gantt-container {
  border: 1px solid #2d2d2d;
  border-radius: 8px;
  overflow-x: auto;
  background: #1a1a1a;
}

.gantt-timeline-header {
  display: flex;
  background: #2d2d2d;
  min-height: 50px;
}

.gantt-project-column {
  width: 250px;
  min-width: 250px;
  padding: 1rem;
  font-weight: 600;
  font-size: 0.875rem;
  border-right: 1px solid #2d2d2d;
  display: flex;
  align-items: center;
  color: #ffffff;
  position: sticky;
  left: 0;
  z-index: 3;
  background: #2d2d2d;
}

.gantt-timeline-months {
  display: flex;
  flex: 1;
  min-width: 1200px;
  background: #2d2d2d;
}

.gantt-month {
  flex: 1;
  padding: 0.75rem 0.5rem;
  text-align: center;
  font-weight: 500;
  border-right: 1px solid #2d2d2d;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8125rem;
  line-height: 1.2;
  min-width: 60px;
  color: #d1d5db;
  background: #2d2d2d;
}

.gantt-body {
  background: #1a1a1a;
}

.gantt-row {
  display: flex;
  border-bottom: 1px solid #2d2d2d;
  min-height: 60px;
}

.gantt-row:hover {
  background: rgba(59, 130, 246, 0.05);
}

.gantt-project-info {
  width: 250px;
  min-width: 250px;
  padding: 1rem;
  border-right: 1px solid #2d2d2d;
  background: #1a1a1a;
  position: sticky;
  left: 0;
  z-index: 2;
}

.project-name {
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: #ffffff;
}

.project-dates {
  font-size: 0.875rem;
  color: #9ca3af;
}

.gantt-timeline-row {
  flex: 1;
  position: relative;
  min-width: 1200px;
  padding: 1rem 0;
  background: #1a1a1a;
}

.gantt-bar {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  height: 24px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  color: white;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
  transition: opacity 0.2s ease;
}

.gantt-bar:hover {
  opacity: 0.8;
  transform: translateY(-50%) scale(1.02);
}

.gantt-bar-text {
  white-space: nowrap;
  overflow: hidden;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: #1a1a1a;
  border: 1px solid #2d2d2d;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #2d2d2d;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #ffffff;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #9ca3af;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.15s ease;
}

.modal-close:hover {
  color: #ffffff;
}

.modal-body {
  padding: 1.5rem;
  color: #e5e5e5;
}

.project-status {
  margin-bottom: 1rem;
}

.status-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
}

.status-badge.status-planning {
  background: rgba(99, 102, 241, 0.2);
  color: #a5b4fc;
}

.status-badge.status-active {
  background: rgba(16, 185, 129, 0.2);
  color: #6ee7b7;
}

.status-badge.status-on_hold {
  background: rgba(245, 158, 11, 0.2);
  color: #fbbf24;
}

.status-badge.status-completed {
  background: rgba(34, 197, 94, 0.2);
  color: #4ade80;
}

.status-badge.status-cancelled {
  background: rgba(239, 68, 68, 0.2);
  color: #f87171;
}

.project-description {
  color: #9ca3af;
  line-height: 1.5;
  margin-bottom: 1.5rem;
}

.progress-section {
  margin-bottom: 1.5rem;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.progress-label {
  font-weight: 500;
  color: #d1d5db;
}

.progress-percentage {
  font-weight: 600;
  color: #ffffff;
}

.progress-bar-container {
  width: 100%;
  height: 8px;
  background: #2d2d2d;
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
  background: #3b82f6;
}

.project-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-label {
  font-weight: 500;
  color: #d1d5db;
}

.detail-value {
  color: #9ca3af;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1.5rem;
  border-top: 1px solid #2d2d2d;
}

.btn-secondary {
  padding: 10px 16px;
  border: 1px solid #374151;
  background: #2d2d2d;
  color: #d1d5db;
  border-radius: 6px;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.15s ease;
}

.btn-secondary:hover {
  background: #374151;
  color: #ffffff;
  border-color: #9ca3af;
}

.btn-primary {
  padding: 10px 16px;
  border: none;
  background: #3b82f6;
  color: white;
  border-radius: 6px;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.15s ease;
}

.btn-primary:hover {
  background: #2563eb;
}

/* These styles are already defined above - removing duplicates */

/* Duplicate styles removed - already defined above */

/* Duplicate styles removed - already defined above with dark theme colors */

.gantt-legend {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #2d2d2d;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #9ca3af;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.gantt-empty {
  text-align: center;
  padding: 3rem;
  color: #9ca3af;
  background: #1a1a1a;
  border-radius: 8px;
  border: 1px solid #2d2d2d;
}

/* Scrollbar styling now handled globally in App.css */

/* Responsive design */
@media (max-width: 768px) {
  .gantt-chart {
    padding: 1rem;
  }

  .gantt-controls {
    margin-bottom: 1rem;
  }

  .gantt-project-column,
  .gantt-project-info {
    width: 200px;
    min-width: 200px;
  }

  .gantt-container {
    overflow-x: auto;
  }

  .gantt-timeline-months {
    min-width: 600px;
  }

  .gantt-legend {
    flex-wrap: wrap;
    gap: 1rem;
  }

  .project-name {
    font-size: 0.8rem;
  }

  .project-dates {
    font-size: 0.7rem;
  }
}

@media (max-width: 480px) {
  .gantt-project-column,
  .gantt-project-info {
    width: 150px;
    min-width: 150px;
  }

  .gantt-legend {
    flex-direction: column;
    align-items: center;
  }
}
