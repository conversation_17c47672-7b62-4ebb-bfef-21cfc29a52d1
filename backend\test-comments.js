const { AICommentManager } = require('./ai-comments');

async function testCommentSystem() {
  console.log('🧪 Testing AI Comment System...\n');
  
  const commentManager = new AICommentManager();
  const projectId = 'test-project-123';
  const storyId = 'test-story-456';
  
  try {
    // Test 1: Add initial comment
    console.log('1. Adding initial comment...');
    const comment1 = await commentManager.addComment(
      projectId,
      'user_story',
      storyId,
      'ProductOwnerAgent',
      'Starting story refinement process. Analyzing requirements and previous feedback.',
      'progress',
      { story_points: 5, priority: 'high' }
    );
    console.log('✅ Comment added:', comment1.id);
    
    // Test 2: Add decision comment
    console.log('\n2. Adding decision comment...');
    const comment2 = await commentManager.logDecision(
      projectId,
      'user_story',
      storyId,
      'ScrumMasterAgent',
      'Task breakdown completed',
      'Created 3 tasks with total estimated effort of 12 hours',
      { task_count: 3, total_hours: 12 }
    );
    console.log('✅ Decision logged:', comment2.id);
    
    // Test 3: Add blocker comment
    console.log('\n3. Adding blocker comment...');
    const comment3 = await commentManager.addComment(
      projectId,
      'user_story',
      storyId,
      'LeadDeveloperAgent',
      'Dependency issue found: Missing authentication service',
      'blocker',
      { dependency: 'auth-service', impact: 'high' },
      { priority: 'critical', mentions: ['ProductOwnerAgent'] }
    );
    console.log('✅ Blocker added:', comment3.id);
    
    // Test 4: Read all comments
    console.log('\n4. Reading all comments...');
    const allComments = await commentManager.readComments(projectId, 'user_story', storyId);
    console.log(`✅ Found ${allComments.length} comments`);
    
    // Test 5: Get comments summary
    console.log('\n5. Getting comments summary...');
    const summary = await commentManager.getCommentsSummary(projectId, 'user_story', storyId);
    console.log('✅ Summary generated:');
    console.log(summary);
    
    // Test 6: Check for blockers
    console.log('\n6. Checking for blockers...');
    const blockers = await commentManager.getBlockers(projectId, 'user_story', storyId);
    console.log(`✅ Found ${blockers.length} blockers`);
    
    // Test 7: Check mentions
    console.log('\n7. Checking mentions for ProductOwnerAgent...');
    const mentions = await commentManager.getUnreadMentions(projectId, 'ProductOwnerAgent');
    console.log(`✅ Found ${mentions.length} unread mentions`);
    
    console.log('\n🎉 All tests passed! Comment system is working correctly.');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testCommentSystem();
