.properties-panel {
  width: 360px;
  height: 100vh;
  background: #1a1a1a;
  border-left: 1px solid #2d2d2d;
  display: flex;
  flex-direction: column;
  color: #e5e5e5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 16px;
  border-bottom: 1px solid #2d2d2d;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.edit-btn, .close-btn {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  transition: all 0.15s ease;
}

.edit-btn:hover, .close-btn:hover {
  background: #2d2d2d;
  color: #ffffff;
}

.panel-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.empty-selection {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  text-align: center;
  color: #6b7280;
}

.empty-selection svg {
  color: #4b5563;
  margin-bottom: 16px;
}

.empty-selection h4 {
  margin: 0 0 8px 0;
  color: #9ca3af;
  font-size: 16px;
  font-weight: 600;
}

.empty-selection p {
  margin: 0;
  font-size: 14px;
}

.property-section {
  margin-bottom: 24px;
}

.story-header {
  margin-bottom: 20px;
}

.story-id {
  background: #374151;
  color: #d1d5db;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  font-family: 'Monaco', 'Menlo', monospace;
  margin-bottom: 8px;
  display: inline-block;
}

.story-title {
  /* margin: 8px 0 0 0; */
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  line-height: 1.4;
}

.title-input {
  width: 100%;
  background: #2d2d2d;
  border: 1px solid #374151;
  border-radius: 6px;
  padding: 12px;
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
  margin-top: 8px;
}

.title-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.properties-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 24px;
}

.property-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.property-item label {
  font-size: 12px;
  font-weight: 500;
  color: #9ca3af;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.property-value {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #d1d5db;
  font-size: 14px;
}

.property-value svg {
  color: #6b7280;
  flex-shrink: 0;
}

.status-indicator, .priority-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.property-select, .property-input {
  background: #2d2d2d;
  border: 1px solid #374151;
  border-radius: 4px;
  padding: 8px 10px;
  color: #ffffff;
  font-size: 14px;
}

.property-select:focus, .property-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.description-content {
  background: #262626;
  border: 1px solid #374151;
  border-radius: 6px;
  padding: 12px;
  color: #d1d5db;
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
  min-height: 60px;
}

.description-textarea {
  width: 100%;
  background: #2d2d2d;
  border: 1px solid #374151;
  border-radius: 6px;
  padding: 12px;
  color: #ffffff;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
  font-family: inherit;
}

.description-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.status-icon.implemented {
  color: #10b981;
}

.status-icon.modified {
  color: #fbbf24;
}

.status-icon.not-started {
  color: #6b7280;
}

.action-buttons {
  display: flex;
  gap: 12px;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #2d2d2d;
}

.btn-primary {
  flex: 1;
  padding: 10px 16px;
  background: #3b82f6;
  border: 1px solid #3b82f6;
  border-radius: 6px;
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
}

.btn-primary:hover {
  background: #2563eb;
  border-color: #2563eb;
}

.btn-secondary {
  flex: 1;
  padding: 10px 16px;
  background: #2d2d2d;
  border: 1px solid #374151;
  border-radius: 6px;
  color: #d1d5db;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
}

.btn-secondary:hover {
  background: #374151;
  border-color: #4b5563;
  color: #ffffff;
}

.btn-danger {
  flex: 1;
  padding: 10px 16px;
  background: #dc2626;
  border: 1px solid #dc2626;
  border-radius: 6px;
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
}

.btn-danger:hover {
  background: #b91c1c;
  border-color: #b91c1c;
}

/* Scrollbar styling now handled globally in App.css */

/* AI Section */
.ai-section {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #2d2d2d;
}

.ai-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  display: flex;
  align-items: center;
  gap: 8px;
}

.ai-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.btn-ai {
  background: #6f42c1;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.btn-ai:hover:not(:disabled) {
  background: #5a2d91;
}

.btn-ai:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-comments {
  background: #374151;
  color: #d1d5db;
  border: 1px solid #4b5563;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.btn-comments:hover {
  background: #4b5563;
  border-color: #6b7280;
  color: #ffffff;
}

.comments-section {
  margin-top: 12px;
  border: 1px solid #2d2d2d;
  border-radius: 6px;
  overflow: hidden;
}

/* Responsive */
@media (max-width: 1200px) {
  .properties-panel {
    width: 320px;
  }

  .properties-grid {
    grid-template-columns: 1fr;
  }
}
