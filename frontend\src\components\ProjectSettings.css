.project-settings {
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
  color: #e5e5e5;
}

.settings-header {
  margin-bottom: 2rem;
  border-bottom: 1px solid #2d2d2d;
  padding-bottom: 1rem;
}

.settings-header h2 {
  margin: 0 0 0.5rem 0;
  color: #ffffff;
  font-size: 1.75rem;
  font-weight: 600;
}

.settings-header p {
  margin: 0;
  color: #9ca3af;
  font-size: 1rem;
}

.settings-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.settings-section {
  background: #1a1a1a;
  border: 1px solid #2d2d2d;
  border-radius: 8px;
  padding: 1.5rem;
}

.settings-section h3 {
  margin: 0 0 1rem 0;
  color: #ffffff;
  font-size: 1.25rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.project-info-grid,
.ai-config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.info-item,
.config-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-item label,
.config-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #9ca3af;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-item span,
.config-item span {
  font-size: 1rem;
  color: #e5e5e5;
}

.status-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: capitalize;
}

.status-badge.status-active {
  background: #10b981;
  color: #ffffff;
}

.status-badge.status-planning {
  background: #3b82f6;
  color: #ffffff;
}

.status-badge.status-completed {
  background: #6b7280;
  color: #ffffff;
}

.status-badge.status-on_hold {
  background: #f59e0b;
  color: #ffffff;
}

.status-indicator {
  font-weight: 500;
}

.status-indicator.enabled {
  color: #10b981;
}

.status-indicator.disabled {
  color: #ef4444;
}

/* Danger Zone */
.danger-zone {
  border-color: #dc2626;
  background: #1a0f0f;
}

.danger-zone h3 {
  color: #ef4444;
}

.danger-zone p {
  color: #d1d5db;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.danger-zone ul {
  color: #d1d5db;
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.danger-zone li {
  margin-bottom: 0.5rem;
}

.delete-project-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: #dc2626;
  color: #ffffff;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
}

.delete-project-btn:hover {
  background: #b91c1c;
}

.delete-confirmation {
  margin-top: 1rem;
  padding: 1.5rem;
  background: #2d1b1b;
  border: 1px solid #dc2626;
  border-radius: 8px;
}

.confirmation-content h4 {
  margin: 0 0 1rem 0;
  color: #ef4444;
  font-size: 1.125rem;
  font-weight: 600;
}

.confirmation-content p {
  margin-bottom: 1rem;
  color: #d1d5db;
  line-height: 1.5;
}

.deletion-preview {
  background: #1a1a1a;
  border: 1px solid #374151;
  border-radius: 6px;
  padding: 1rem;
  margin: 1rem 0;
}

.deletion-preview h5 {
  margin: 0 0 0.5rem 0;
  color: #ef4444;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.deletion-preview ul {
  margin: 0;
  padding-left: 1.5rem;
  color: #d1d5db;
}

.deletion-preview li {
  margin-bottom: 0.25rem;
  font-size: 0.875rem;
}

.delete-confirm-input {
  width: 100%;
  padding: 0.75rem;
  background: #1a1a1a;
  border: 1px solid #374151;
  border-radius: 6px;
  color: #e5e5e5;
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.delete-confirm-input:focus {
  outline: none;
  border-color: #dc2626;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.confirmation-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.confirmation-buttons .btn-danger {
  background: #dc2626;
  color: #ffffff;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.confirmation-buttons .btn-danger:hover:not(:disabled) {
  background: #b91c1c;
}

.confirmation-buttons .btn-danger:disabled {
  background: #6b7280;
  cursor: not-allowed;
}

.confirmation-buttons .btn-secondary {
  background: #374151;
  color: #e5e5e5;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
}

.confirmation-buttons .btn-secondary:hover {
  background: #4b5563;
}

@media (max-width: 768px) {
  .project-settings {
    padding: 1rem;
  }
  
  .project-info-grid,
  .ai-config-grid {
    grid-template-columns: 1fr;
  }
  
  .confirmation-buttons {
    flex-direction: column;
  }
  
  .confirmation-buttons .btn-danger,
  .confirmation-buttons .btn-secondary {
    width: 100%;
    justify-content: center;
  }
}
