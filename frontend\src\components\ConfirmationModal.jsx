import { X, AlertTriangle } from 'lucide-react';
import './ConfirmationModal.css';

const ConfirmationModal = ({ 
  isOpen, 
  onClose, 
  onConfirm, 
  title = "Confirm Action",
  message = "Are you sure you want to proceed?",
  confirmText = "Confirm",
  cancelText = "Cancel",
  type = "default" // default, danger, warning
}) => {
  if (!isOpen) return null;

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const getIconForType = () => {
    switch (type) {
      case 'danger':
        return <AlertTriangle size={24} className="modal-icon danger" />;
      case 'warning':
        return <AlertTriangle size={24} className="modal-icon warning" />;
      default:
        return null;
    }
  };

  return (
    <div className="modal-backdrop" onClick={handleBackdropClick}>
      <div className="confirmation-modal">
        <div className="modal-header">
          <div className="modal-title-section">
            {getIconForType()}
            <h3 className="modal-title">{title}</h3>
          </div>
          <button 
            onClick={onClose}
            className="modal-close-button"
            aria-label="Close modal"
          >
            <X size={20} />
          </button>
        </div>
        
        <div className="modal-body">
          <p className="modal-message">{message}</p>
        </div>
        
        <div className="modal-footer">
          {cancelText && (
            <button
              onClick={onClose}
              className="btn-secondary"
            >
              {cancelText}
            </button>
          )}
          <button
            onClick={onConfirm}
            className={`btn-primary ${type === 'danger' ? 'btn-danger' : ''}`}
          >
            {confirmText}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConfirmationModal;
