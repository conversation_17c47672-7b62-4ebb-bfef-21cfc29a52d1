/* Markdown Viewer Styles */
.markdown-viewer {
  width: 100%;
  height: 100%;
  overflow-y: auto;
}

.empty-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #6b7280;
  font-style: italic;
}

.markdown-content {
  max-width: 800px;
  margin: 0 auto;
  color: #e5e5e5;
  line-height: 1.6;
  font-size: 16px;
}

/* Typography */
.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  color: #ffffff;
  margin-top: 32px;
  margin-bottom: 16px;
  font-weight: 600;
  line-height: 1.3;
}

.markdown-content h1 {
  font-size: 32px;
  border-bottom: 2px solid #2d2d2d;
  padding-bottom: 16px;
  margin-top: 0;
}

.markdown-content h2 {
  font-size: 26px;
  border-bottom: 1px solid #2d2d2d;
  padding-bottom: 8px;
}

.markdown-content h3 {
  font-size: 22px;
  color: #f3f4f6;
}

.markdown-content h4 {
  font-size: 18px;
  color: #f3f4f6;
}

.markdown-content h5 {
  font-size: 16px;
  color: #f3f4f6;
}

.markdown-content h6 {
  font-size: 14px;
  color: #f3f4f6;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Paragraphs and text */
.markdown-content p {
  margin-bottom: 16px;
  color: #e5e5e5;
}

.markdown-content strong {
  color: #ffffff;
  font-weight: 600;
}

.markdown-content em {
  color: #d1d5db;
  font-style: italic;
}

/* Links */
.markdown-content a {
  color: #3b82f6;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.2s ease;
}

.markdown-content a:hover {
  color: #60a5fa;
  border-bottom-color: #60a5fa;
}

/* Lists */
.markdown-content ul,
.markdown-content ol {
  margin-bottom: 16px;
  padding-left: 24px;
}

.markdown-content ul {
  list-style-type: disc;
}

.markdown-content ol {
  list-style-type: decimal;
}

.markdown-content li {
  margin-bottom: 8px;
  color: #e5e5e5;
}

.markdown-content li::marker {
  color: #9ca3af;
}

/* Nested lists */
.markdown-content ul ul,
.markdown-content ol ol,
.markdown-content ul ol,
.markdown-content ol ul {
  margin-top: 8px;
  margin-bottom: 8px;
}

/* Code */
.markdown-content .inline-code {
  background: #2d2d2d;
  color: #f8fafc;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
  font-size: 14px;
  border: 1px solid #404040;
}

.markdown-content .code-block {
  background: #1a1a1a;
  border: 1px solid #2d2d2d;
  border-radius: 8px;
  padding: 16px;
  overflow-x: auto;
  margin: 16px 0;
  position: relative;
}

.markdown-content .code-block code {
  background: none;
  padding: 0;
  border: none;
  color: #f8fafc;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
  font-size: 14px;
  line-height: 1.5;
  white-space: pre;
}

/* Language-specific syntax highlighting */
.markdown-content .language-javascript,
.markdown-content .language-js {
  color: #fbbf24;
}

.markdown-content .language-python {
  color: #34d399;
}

.markdown-content .language-css {
  color: #60a5fa;
}

.markdown-content .language-html {
  color: #f87171;
}

.markdown-content .language-json {
  color: #a78bfa;
}

/* Blockquotes */
.markdown-content blockquote {
  border-left: 4px solid #3b82f6;
  padding: 16px 20px;
  margin: 16px 0;
  background: #1a1a1a;
  border-radius: 0 8px 8px 0;
  color: #d1d5db;
  font-style: italic;
  position: relative;
}

.markdown-content blockquote::before {
  content: '"';
  font-size: 48px;
  color: #3b82f6;
  position: absolute;
  top: -8px;
  left: 16px;
  opacity: 0.3;
}

.markdown-content blockquote p {
  margin: 0;
  padding-left: 20px;
}

/* Tables */
.markdown-content .markdown-table {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
  background: #1a1a1a;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #2d2d2d;
}

.markdown-content .markdown-table th,
.markdown-content .markdown-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #2d2d2d;
}

.markdown-content .markdown-table th {
  background: #2d2d2d;
  font-weight: 600;
  color: #ffffff;
  border-bottom: 2px solid #404040;
}

.markdown-content .markdown-table td {
  color: #e5e5e5;
}

.markdown-content .markdown-table tr:hover {
  background: #262626;
}

.markdown-content .markdown-table tr:last-child td {
  border-bottom: none;
}

/* Horizontal rules */
.markdown-content hr {
  border: none;
  border-top: 2px solid #2d2d2d;
  margin: 32px 0;
  opacity: 0.7;
}

/* Images */
.markdown-content img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 16px 0;
  border: 1px solid #2d2d2d;
}

/* Task lists (if supported) */
.markdown-content input[type="checkbox"] {
  margin-right: 8px;
  accent-color: #3b82f6;
}

/* Responsive design */
@media (max-width: 768px) {
  .markdown-content {
    padding: 16px;
    font-size: 14px;
  }
  
  .markdown-content h1 {
    font-size: 24px;
  }
  
  .markdown-content h2 {
    font-size: 20px;
  }
  
  .markdown-content h3 {
    font-size: 18px;
  }
  
  .markdown-content .code-block {
    padding: 12px;
    font-size: 12px;
  }
  
  .markdown-content .markdown-table {
    font-size: 12px;
  }
  
  .markdown-content .markdown-table th,
  .markdown-content .markdown-table td {
    padding: 8px 12px;
  }
}

/* Scrollbar styling now handled globally in App.css */
