# Project Goals: AI-Driven Development Assistant

## Overview

The original goal of this project is to create a new kind of coding assistant, similar to Augment, but with a development process driven entirely by an AI team based on user stories. The human user's role is that of a Product Owner, who provides high-level requirements in the form of plain-text user stories, bugs, and versioning information.

## The AI Development Team

The core of the system is a collaborative team of specialized AI agents, each with a distinct role and prompt-driven goals:

*   **Lead Developer:** This AI agent is the head of the development team. It is responsible for interpreting the user stories, creating a development plan, and spinning up and managing a team of `ProfessionalWorker` agents to execute the plan.
*   **Professional Workers:** These are the workhorse AI agents that perform the actual coding tasks. They are created and assigned tasks by the `Lead Developer`.
*   **AI Scrum Master:** This agent's primary goal is to optimize the development process for efficiency and cost-effectiveness. It is responsible for:
    *   Prioritizing the work scheduled by the Product Owner.
    *   Building and delivering reports on development progress to the human user.
    *   Managing the overall workflow to ensure the best possible results for the lowest possible AI token cost.

## The Workflow

The intended workflow is a fully automated, iterative development cycle:

1.  The human **Product Owner** provides user stories and prioritizes them in a task-like interface.
2.  The **AI Scrum Master** takes the prioritized list and schedules the work for the AI development team.
3.  The **Lead Developer** analyzes the scheduled tasks and breaks them down into smaller, actionable coding tasks.
4.  The **Lead Developer** assigns these tasks to a team of **Professional Workers**.
5.  The AI team works on the code, implementing features and fixing bugs in an iterative cycle. An iteration could last for an entire day if the task list is large.
6.  The human Product Owner periodically checks the results and provides feedback, creating new user stories as needed.

The entire system is designed to be highly automated, minimizing the need for human intervention beyond the initial high-level direction provided by the Product Owner.
