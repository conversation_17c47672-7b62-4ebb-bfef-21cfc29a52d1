# AGENTS.md

This file contains instructions for AI agents working on this project.

## General Guidelines

- The project is built using a Node.js backend and a React frontend.
- The backend code is located in the `backend/` directory.
- The frontend code is located in the `frontend/` directory.
- User stories are located in the `tasks/` directory.
- Reports are located in the `reports/` directory.

## Development Process

1.  The Product Owner AI creates user stories in the `tasks/` directory.
2.  The Scrum Master AI prioritizes the tasks and creates a plan.
3.  The Lead Developer AI assigns tasks to Professional Worker AIs.
4.  The Professional Worker AIs implement the features and write tests.
5.  The Lead Developer AI reviews the code and merges it.
6.  The Scrum Master AI generates reports on the progress.
