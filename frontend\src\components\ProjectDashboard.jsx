import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Plus, Calendar, Users, CheckCircle, Clock, TrendingUp, FolderOpen } from 'lucide-react';
import { format, differenceInDays, parseISO } from 'date-fns';
import DashboardSidebar from './DashboardSidebar';
import ProjectModal from './ProjectModal';
import GanttChart from './GanttChart';
import AISchedulerControl from './AISchedulerControl';
import './ProjectDashboard.css';

const ProjectDashboard = () => {
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const [activeView, setActiveView] = useState(location.state?.activeView || 'overview');

  useEffect(() => {
    fetchProjects();
  }, []);

  // Handle navigation state changes
  useEffect(() => {
    if (location.state?.activeView) {
      setActiveView(location.state.activeView);
      // Clear the state to prevent issues with browser back/forward
      window.history.replaceState({}, document.title);
    }
  }, [location.state]);

  const fetchProjects = async () => {
    try {
      const response = await fetch('/api/projects');
      const data = await response.json();
      setProjects(data.projects || []);
    } catch (error) {
      console.error('Error fetching projects:', error);
    } finally {
      setLoading(false);
    }
  };

  const getProjectStats = (project) => {
    const now = new Date();
    const startDate = project.start_date ? parseISO(project.start_date) : null;
    const endDate = project.end_date ? parseISO(project.end_date) : null;
    
    let daysRemaining = 0;
    let totalDays = 0;
    
    if (startDate && endDate) {
      totalDays = differenceInDays(endDate, startDate);
      daysRemaining = differenceInDays(endDate, now);
    }

    return {
      daysRemaining: Math.max(0, daysRemaining),
      totalDays,
      progress: project.progress || 0
    };
  };

  const getStatusColor = (status) => {
    const colors = {
      planning: '#6366f1',
      active: '#10b981',
      on_hold: '#f59e0b',
      completed: '#22c55e',
      cancelled: '#ef4444'
    };
    return colors[status] || '#6b7280';
  };

  const handleProjectClick = (projectId) => {
    navigate(`/projects/${projectId}`);
  };

  const handleProjectCreate = (newProject) => {
    setProjects([newProject, ...projects]);
    setShowCreateModal(false);
  };

  const getDashboardStats = () => {
    return {
      total: projects.length,
      active: projects.filter(p => p.status === 'active').length,
      completed: projects.filter(p => p.status === 'completed').length,
      planning: projects.filter(p => p.status === 'planning').length,
      onHold: projects.filter(p => p.status === 'on_hold').length
    };
  };

  if (loading) {
    return (
      <div className="dashboard-loading">
        <div className="loading-spinner"></div>
        <p>Loading projects...</p>
      </div>
    );
  }

  // Render the new sidebar layout
  return (
    <div className="project-detail-modern">
      {/* Left Sidebar */}
      <DashboardSidebar
        activeView={activeView}
        onViewChange={(view) => {
          if (view === 'reports') {
            navigate('/reports');
          } else {
            setActiveView(view);
          }
        }}
        stats={getDashboardStats()}
        onCreateProject={() => setShowCreateModal(true)}
      />

      {/* Main Content Area */}
      <div className="main-content">
        {activeView === 'overview' && (
          <div className="dashboard-overview">
            <div className="dashboard-header">
              <h1>Dashboard Overview</h1>
              <p>Welcome to your project management dashboard</p>
            </div>

            <div className="dashboard-stats">
              <div className="stat-card">
                <div className="stat-icon">
                  <CheckCircle size={24} />
                </div>
                <div className="stat-content">
                  <h3>{projects.filter(p => p.status === 'completed').length}</h3>
                  <p>Completed</p>
                </div>
              </div>
              <div className="stat-card">
                <div className="stat-icon">
                  <Clock size={24} />
                </div>
                <div className="stat-content">
                  <h3>{projects.filter(p => p.status === 'active').length}</h3>
                  <p>Active</p>
                </div>
              </div>
              <div className="stat-card">
                <div className="stat-icon">
                  <Calendar size={24} />
                </div>
                <div className="stat-content">
                  <h3>{projects.filter(p => p.status === 'planning').length}</h3>
                  <p>Planning</p>
                </div>
              </div>
              <div className="stat-card">
                <div className="stat-icon">
                  <TrendingUp size={24} />
                </div>
                <div className="stat-content">
                  <h3>{projects.length}</h3>
                  <p>Total Projects</p>
                </div>
              </div>
            </div>

            {projects.length === 0 && (
              <div className="empty-state">
                <div className="empty-icon">
                  <Calendar size={48} />
                </div>
                <h3>No projects yet</h3>
                <p>Create your first project to get started with project management</p>
                <button
                  className="create-first-project-btn"
                  onClick={() => setShowCreateModal(true)}
                >
                  <Plus size={20} />
                  Create Your First Project
                </button>
              </div>
            )}
          </div>
        )}

        {activeView === 'projects' && (
          <div className="dashboard-projects">
            <div className="dashboard-header">
              <h1>All Projects</h1>
              <p>Manage and view all your projects</p>
            </div>

            <div className="projects-grid">
              {projects.map((project) => {
                const stats = getProjectStats(project);
                return (
                  <div
                    key={project.id}
                    className="project-card"
                    onClick={() => handleProjectClick(project.id)}
                  >
                    <div className="project-header">
                      <h3>{project.name}</h3>
                      <span
                        className="project-status"
                        style={{ backgroundColor: getStatusColor(project.status) }}
                      >
                        {project.status}
                      </span>
                    </div>
                    <p className="project-description">{project.description}</p>
                    <div className="project-progress">
                      <div className="progress-header">
                        <span>Progress</span>
                        <span>{stats.progress}%</span>
                      </div>
                      <div className="progress-bar">
                        <div
                          className="progress-fill"
                          style={{ width: `${stats.progress}%` }}
                        />
                      </div>
                    </div>
                    <div className="project-timeline">
                      <div className="timeline-item">
                        <Calendar size={16} />
                        <span>
                          {project.start_date
                            ? format(parseISO(project.start_date), 'MMM dd, yyyy')
                            : 'No start date'}
                        </span>
                      </div>
                      <div className="timeline-item">
                        <Clock size={16} />
                        <span>{stats.daysRemaining} days remaining</span>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {activeView === 'timeline' && (
          <div className="dashboard-timeline">
            <div className="dashboard-header">
              <h1>Project Timeline</h1>
              <p>View project schedules and milestones</p>
            </div>

            <GanttChart
              projects={projects}
              onProjectClick={(project) => navigate(`/projects/${project.id}`)}
            />
          </div>
        )}

        {activeView === 'ai-scheduler' && (
          <AISchedulerControl />
        )}

        {activeView === 'settings' && (
          <div className="coming-soon">
            <h2>Settings</h2>
            <p>This feature is coming soon!</p>
          </div>
        )}

      </div>

      <ProjectModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSave={handleProjectCreate}
      />
    </div>
  );
};

export default ProjectDashboard;
