.comment-timeline {
  background: #2d2d2d;
  border: 1px solid #374151;
  border-radius: 8px;
  margin: 16px 0;
}

.timeline-header {
  padding: 12px 16px;
  border-bottom: 1px solid #374151;
  background: #2d2d2d;
  border-radius: 8px 8px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.timeline-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.expand-toggle {
  background: none;
  border: none;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
  font-weight: 500;
  color: #e5e5e5;
}

.expand-toggle:hover {
  background: #374151;
}

.timeline-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filter-select {
  padding: 4px 8px;
  border: 1px solid #374151;
  border-radius: 4px;
  font-size: 13px;
  background: #1a1a1a;
  color: #e5e5e5;
}

.filter-select:focus {
  outline: none;
  border-color: #4b5563;
}

.internal-toggle {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border: 1px solid #374151;
  border-radius: 4px;
  background: #1a1a1a;
  color: #e5e5e5;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.internal-toggle:hover {
  background: #374151;
}

.internal-toggle.active {
  background: #2563eb;
  color: white;
  border-color: #2563eb;
}

.timeline-content {
  overflow-y: auto;
  padding: 16px;
  background: #2d2d2d;
}

.no-comments {
  text-align: center;
  padding: 32px;
  color: #9ca3af;
}

.no-comments svg {
  margin-bottom: 8px;
  opacity: 0.5;
}

.timeline-items {
  position: relative;
}

.timeline-items::before {
  content: '';
  position: absolute;
  left: 12px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #374151;
}

.timeline-item {
  position: relative;
  display: flex;
  margin-bottom: 16px;
  padding-left: 8px;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-marker {
  position: relative;
  z-index: 1;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #1a1a1a;
  border: 2px solid #374151;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
}

.timeline-content-item {
  flex: 1;
  background: #1a1a1a;
  border: 1px solid #374151;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
  flex-wrap: wrap;
  gap: 8px;
}

.comment-author {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.author-name {
  font-weight: 600;
  color: #e5e5e5;
}

.comment-type {
  background: #374151;
  color: #e5e5e5;
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 11px;
  text-transform: uppercase;
  font-weight: 500;
}

.priority-badge {
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 10px;
  text-transform: uppercase;
  font-weight: 600;
}

.priority-badge.high {
  background: #f59e0b;
  color: #1f2937;
}

.priority-badge.critical {
  background: #dc2626;
  color: white;
}

.comment-time {
  font-size: 12px;
  color: #9ca3af;
}

.comment-text {
  color: #e5e5e5;
  line-height: 1.5;
  margin-bottom: 8px;
}

.decision-format {
  background: #374151;
  border-left: 4px solid #10b981;
  padding: 8px 12px;
  border-radius: 4px;
}

.decision-title {
  margin-bottom: 8px;
  color: #e5e5e5;
}

.decision-reasoning {
  font-size: 14px;
  color: #d1d5db;
}

.comment-metadata {
  margin-top: 8px;
}

.comment-metadata details {
  font-size: 12px;
}

.comment-metadata summary {
  cursor: pointer;
  color: #9ca3af;
  font-weight: 500;
}

.comment-metadata pre {
  background: #374151;
  color: #e5e5e5;
  padding: 8px;
  border-radius: 4px;
  margin-top: 4px;
  font-size: 11px;
  overflow-x: auto;
}

/* Comment type specific styles */
.timeline-item.blocker .timeline-marker {
  border-color: #dc2626;
  background: #7f1d1d;
}

.timeline-item.blocker .comment-icon {
  color: #f87171;
}

.timeline-item.critical .timeline-marker {
  border-color: #dc2626;
  background: #7f1d1d;
}

.timeline-item.critical .comment-icon {
  color: #f87171;
}

.timeline-item.decision .timeline-marker {
  border-color: #10b981;
  background: #065f46;
}

.timeline-item.decision .comment-icon {
  color: #34d399;
}

.timeline-item.progress .timeline-marker {
  border-color: #2563eb;
  background: #1e3a8a;
}

.timeline-item.progress .comment-icon {
  color: #60a5fa;
}

.timeline-item.update .timeline-marker {
  border-color: #7c3aed;
  background: #581c87;
}

.timeline-item.update .comment-icon {
  color: #a78bfa;
}

.comment-icon {
  width: 14px;
  height: 14px;
}

.comment-icon.ai {
  color: #a78bfa;
}

.comment-icon.human {
  color: #e5e5e5;
}

.loading-spinner {
  text-align: center;
  padding: 32px;
  color: #9ca3af;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px;
  background: #7f1d1d;
  color: #f87171;
  border-radius: 4px;
  margin: 16px;
}

/* Responsive design */
@media (max-width: 768px) {
  .timeline-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .timeline-controls {
    justify-content: space-between;
  }
  
  .comment-header {
    flex-direction: column;
    align-items: flex-start;
  }
}
