/* Reports Page Styles */
.reports-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

/* Header */
.reports-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24px 32px;
  border-bottom: 1px solid #2d2d2d;
  background: #1a1a1a;
}

.header-content h1 {
  margin: 0 0 8px 0;
  color: #ffffff;
  font-size: 28px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 12px;
}

.reports-count {
  font-size: 18px;
  color: #6b7280;
  font-weight: 500;
}

.header-content p {
  margin: 0;
  color: #9ca3af;
  font-size: 16px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: #2d2d2d;
  border: 1px solid #404040;
  border-radius: 6px;
  color: #e5e5e5;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-button:hover {
  background: #404040;
  border-color: #525252;
}

/* Controls */
.reports-controls {
  display: flex;
  gap: 16px;
  padding: 20px 32px;
  background: #1a1a1a;
  border-bottom: 1px solid #2d2d2d;
}

.search-container {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
}

.search-input {
  width: 100%;
  padding: 10px 12px 10px 40px;
  background: #2d2d2d;
  border: 1px solid #404040;
  border-radius: 6px;
  color: #e5e5e5;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  border-color: #3b82f6;
}

.search-input::placeholder {
  color: #6b7280;
}

.filter-container {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-icon {
  color: #6b7280;
}

.filter-select {
  padding: 10px 12px;
  background: #2d2d2d;
  border: 1px solid #404040;
  border-radius: 6px;
  color: #e5e5e5;
  font-size: 14px;
  cursor: pointer;
  outline: none;
  transition: border-color 0.2s ease;
}

.filter-select:focus {
  border-color: #3b82f6;
}

/* Content Area */
.reports-content {
  flex: 1;
  display: flex;
  min-height: 0;
}

/* Reports List */
.reports-list {
  width: 400px;
  background: #1a1a1a;
  border-right: 1px solid #2d2d2d;
  overflow-y: auto;
  padding: 16px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  text-align: center;
  color: #6b7280;
}

.empty-state svg {
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  color: #9ca3af;
  font-size: 18px;
  font-weight: 600;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

/* Report Item */
.report-item {
  padding: 16px;
  margin-bottom: 12px;
  background: #2d2d2d;
  border: 1px solid #404040;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.report-item:hover {
  background: #333333;
  border-color: #525252;
}

.report-item.selected {
  background: #1e3a8a;
  border-color: #3b82f6;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.report-type {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.report-actions {
  display: flex;
  gap: 4px;
}

.action-button {
  padding: 4px;
  background: transparent;
  border: none;
  color: #6b7280;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.action-button:hover {
  background: #404040;
  color: #e5e5e5;
}

.action-button.delete-button {
  color: #6b7280;
}

.action-button.delete-button:hover {
  background: #ef4444;
  color: white;
}

.report-title {
  margin: 0 0 12px 0;
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.4;
}

.report-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #6b7280;
  font-size: 12px;
}

.report-summary {
  margin: 0;
  color: #9ca3af;
  font-size: 14px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Report Viewer */
.report-viewer {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #0f0f0f;
  overflow: hidden;
}

.viewer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 1px solid #2d2d2d;
  background: #1a1a1a;
}

.viewer-header h2 {
  margin: 0;
  color: #ffffff;
  font-size: 20px;
  font-weight: 600;
}

.download-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: #3b82f6;
  border: none;
  border-radius: 6px;
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.download-button:hover {
  background: #2563eb;
}

.report-content {
  flex: 1;
  overflow-y: auto;
  padding: 32px;
}

/* Markdown Content Styles */
.markdown-content {
  max-width: 800px;
  margin: 0 auto;
  color: #e5e5e5;
  line-height: 1.6;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  color: #ffffff;
  margin-top: 32px;
  margin-bottom: 16px;
  font-weight: 600;
}

.markdown-content h1 {
  font-size: 32px;
  border-bottom: 1px solid #2d2d2d;
  padding-bottom: 16px;
}

.markdown-content h2 {
  font-size: 24px;
}

.markdown-content h3 {
  font-size: 20px;
}

.markdown-content p {
  margin-bottom: 16px;
}

.markdown-content ul,
.markdown-content ol {
  margin-bottom: 16px;
  padding-left: 24px;
}

.markdown-content li {
  margin-bottom: 8px;
}

.markdown-content code {
  background: #2d2d2d;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
}

.markdown-content pre {
  background: #1a1a1a;
  border: 1px solid #2d2d2d;
  border-radius: 8px;
  padding: 16px;
  overflow-x: auto;
  margin-bottom: 16px;
}

.markdown-content pre code {
  background: none;
  padding: 0;
}

.markdown-content blockquote {
  border-left: 4px solid #3b82f6;
  padding-left: 16px;
  margin: 16px 0;
  color: #9ca3af;
  font-style: italic;
}

.markdown-content table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 16px;
}

.markdown-content th,
.markdown-content td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #2d2d2d;
}

.markdown-content th {
  background: #1a1a1a;
  font-weight: 600;
  color: #ffffff;
}

.markdown-content strong {
  color: #ffffff;
  font-weight: 600;
}

.markdown-content em {
  color: #d1d5db;
}

.markdown-content hr {
  border: none;
  border-top: 1px solid #2d2d2d;
  margin: 32px 0;
}
