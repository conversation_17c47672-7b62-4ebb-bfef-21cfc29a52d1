const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const DB_PATH = path.join(__dirname, 'database.db');

const db = new sqlite3.Database(DB_PATH, (err) => {
    if (err) {
        console.error('Error opening database', err.message);
    } else {
        console.log('Connected to the SQLite database.');
        // Enable foreign key constraints
        db.run('PRAGMA foreign_keys = ON', (err) => {
            if (err) {
                console.error('Error enabling foreign keys:', err.message);
            } else {
                console.log('Foreign key constraints enabled.');
            }
        });
        // Create projects table with nanoid and AI features
        db.run(`CREATE TABLE IF NOT EXISTS projects (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            description TEXT,
            start_date DATE,
            end_date DATE,
            status TEXT DEFAULT 'planning' CHECK (status IN ('planning', 'active', 'in_progress', 'on_hold', 'completed', 'cancelled')),
            progress INTEGER DEFAULT 0,
            tech_stack TEXT NOT NULL,
            workspace_path TEXT,
            ai_enabled BOOLEAN DEFAULT 1,
            ai_config TEXT DEFAULT '{}',
            ai_processing_enabled BOOLEAN DEFAULT 1,
            ai_last_activity TIMESTAMP,
            ai_processing_status TEXT DEFAULT 'idle' CHECK (ai_processing_status IN ('idle', 'processing', 'paused', 'error')),
            repository_url TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )`, (err) => {
            if (err) {
                console.error('Error creating projects table', err.message);
            }
        });

        // Create tasks table with project association
        db.run(`CREATE TABLE IF NOT EXISTS tasks (
            id TEXT PRIMARY KEY,
            title TEXT NOT NULL,
            description TEXT NOT NULL,
            project_id TEXT,
            status TEXT DEFAULT 'todo',
            priority TEXT DEFAULT 'medium',
            assigned_to TEXT,
            due_date DATE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
        )`, (err) => {
            if (err) {
                console.error('Error creating tasks table', err.message);
            }
        });

        // Create user stories table with version tracking
        db.run(`CREATE TABLE IF NOT EXISTS user_stories (
            id TEXT PRIMARY KEY,
            project_id TEXT NOT NULL,
            title TEXT NOT NULL,
            description TEXT NOT NULL,
            acceptance_criteria TEXT NOT NULL,
            priority TEXT DEFAULT 'medium',
            story_points INTEGER DEFAULT 3,
            epic TEXT,
            user_persona TEXT DEFAULT 'user',
            business_value TEXT,
            status TEXT DEFAULT 'backlog',
            implementation_status TEXT DEFAULT 'not_started',
            implemented_in_version TEXT,
            last_modified_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            needs_reimplementation BOOLEAN DEFAULT FALSE,
            sprint_id TEXT,
            assigned_to TEXT,
            validation_score INTEGER DEFAULT 0,
            validation_feedback TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE,
            FOREIGN KEY (sprint_id) REFERENCES sprints (id) ON DELETE SET NULL
        )`, (err) => {
            if (err) {
                console.error('Error creating user_stories table', err.message);
            }
        });

        // Add validation columns to existing user_stories table if they don't exist
        db.run(`ALTER TABLE user_stories ADD COLUMN validation_score INTEGER DEFAULT 0`, (err) => {
            // Ignore error if column already exists
        });

        db.run(`ALTER TABLE user_stories ADD COLUMN validation_feedback TEXT`, (err) => {
            // Ignore error if column already exists
        });

        // Create sprints table
        db.run(`CREATE TABLE IF NOT EXISTS sprints (
            id TEXT PRIMARY KEY,
            project_id TEXT NOT NULL,
            name TEXT NOT NULL,
            description TEXT,
            start_date DATE NOT NULL,
            end_date DATE NOT NULL,
            status TEXT DEFAULT 'planning' CHECK (status IN ('planning', 'active', 'completed', 'cancelled')),
            goals TEXT,
            capacity_hours INTEGER DEFAULT 40,
            ai_processing_enabled BOOLEAN DEFAULT 1,
            ai_last_activity TIMESTAMP,
            stories_completed INTEGER DEFAULT 0,
            stories_total INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
        )`, (err) => {
            if (err) {
                console.error('Error creating sprints table', err.message);
            }
        });

        // Create sprint reviews table for user feedback
        db.run(`CREATE TABLE IF NOT EXISTS sprint_reviews (
            id TEXT PRIMARY KEY,
            sprint_id TEXT NOT NULL,
            project_id TEXT NOT NULL,
            user_feedback TEXT,
            ai_performance_rating INTEGER,
            completed_stories INTEGER DEFAULT 0,
            total_stories INTEGER DEFAULT 0,
            review_notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (sprint_id) REFERENCES sprints (id) ON DELETE CASCADE,
            FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
        )`, (err) => {
            if (err) {
                console.error('Error creating sprint_reviews table', err.message);
            }
        });

        // Create comments table for AI agent communication and progress tracking
        db.run(`CREATE TABLE IF NOT EXISTS comments (
            id TEXT PRIMARY KEY,
            entity_type TEXT NOT NULL CHECK (entity_type IN ('user_story', 'task', 'sprint', 'project')),
            entity_id TEXT NOT NULL,
            project_id TEXT NOT NULL,
            author_type TEXT NOT NULL CHECK (author_type IN ('ai_agent', 'human')),
            author_name TEXT NOT NULL,
            comment_text TEXT NOT NULL,
            comment_type TEXT DEFAULT 'progress' CHECK (comment_type IN ('progress', 'blocker', 'decision', 'update', 'question', 'resolution', 'dependency')),
            metadata TEXT DEFAULT '{}',
            parent_comment_id TEXT,
            is_internal BOOLEAN DEFAULT FALSE,
            priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'critical')),
            status TEXT DEFAULT 'active' CHECK (status IN ('active', 'resolved', 'archived')),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE,
            FOREIGN KEY (parent_comment_id) REFERENCES comments (id) ON DELETE SET NULL
        )`, (err) => {
            if (err) {
                console.error('Error creating comments table', err.message);
            }
        });

        // Create comment_mentions table for AI agent notifications
        db.run(`CREATE TABLE IF NOT EXISTS comment_mentions (
            id TEXT PRIMARY KEY,
            comment_id TEXT NOT NULL,
            mentioned_agent TEXT NOT NULL,
            is_read BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (comment_id) REFERENCES comments (id) ON DELETE CASCADE
        )`, (err) => {
            if (err) {
                console.error('Error creating comment_mentions table', err.message);
            }
        });

        // Create AI scheduler state table
        db.run(`CREATE TABLE IF NOT EXISTS ai_scheduler_state (
            id TEXT PRIMARY KEY DEFAULT 'singleton',
            is_running BOOLEAN DEFAULT FALSE,
            tick_rate INTEGER DEFAULT 30000,
            config TEXT DEFAULT '{}',
            stats TEXT DEFAULT '{}',
            last_tick TIMESTAMP,
            started_at TIMESTAMP,
            stopped_at TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )`, (err) => {
            if (err) {
                console.error('Error creating ai_scheduler_state table', err.message);
            }
        });

        // Create AI processing queue table
        db.run(`CREATE TABLE IF NOT EXISTS ai_processing_queue (
            id TEXT PRIMARY KEY,
            project_id TEXT NOT NULL,
            entity_type TEXT NOT NULL CHECK (entity_type IN ('user_story', 'sprint', 'project')),
            entity_id TEXT NOT NULL,
            priority INTEGER DEFAULT 5,
            status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
            scheduled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            started_at TIMESTAMP,
            completed_at TIMESTAMP,
            error_message TEXT,
            retry_count INTEGER DEFAULT 0,
            max_retries INTEGER DEFAULT 3,
            metadata TEXT DEFAULT '{}',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
        )`, (err) => {
            if (err) {
                console.error('Error creating ai_processing_queue table', err.message);
            }
        });

        // Create AI prompts table for configurable AI agent prompts
        db.run(`CREATE TABLE IF NOT EXISTS ai_prompts (
            id TEXT PRIMARY KEY,
            project_id TEXT,
            agent_type TEXT NOT NULL CHECK (agent_type IN ('product_owner', 'scrum_master', 'lead_developer')),
            prompt_template TEXT NOT NULL,
            description TEXT,
            is_default BOOLEAN DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE,
            UNIQUE(project_id, agent_type)
        )`, (err) => {
            if (err) {
                console.error('Error creating ai_prompts table', err.message);
            }
        });
    }
});

module.exports = db;
