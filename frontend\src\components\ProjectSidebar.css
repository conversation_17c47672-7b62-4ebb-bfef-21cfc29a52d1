.project-sidebar {
  width: 280px;
  height: 100vh;
  background: #1a1a1a;
  border-right: 1px solid #2d2d2d;
  display: flex;
  flex-direction: column;
  color: #e5e5e5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow: hidden;
}

/* Sidebar Header */
.sidebar-header {
  padding: 20px 16px;
  border-bottom: 1px solid #2d2d2d;
}
.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: #2d2d2d;
  border: 1px solid #374151;
  border-radius: 6px;
  color: #d1d5db;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.15s ease;
}

.back-button:hover {
  background: #374151;
  border-color: #4b5563;
  color: #ffffff;
}


.project-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.project-avatar {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  color: white;
}

.project-details {
  flex: 1;
  min-width: 0;
}

.project-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.project-status {
  font-size: 12px;
  color: #9ca3af;
  text-transform: capitalize;
}

/* Navigation */
.sidebar-nav {
  flex: 1;
  padding: 16px 0;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.nav-section {
  margin-bottom: 24px;
}

.nav-section-header {
  padding: 0 16px 8px;
  font-size: 11px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 8px 16px;
  background: none;
  border: none;
  color: #d1d5db;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.15s ease;
  text-align: left;
}

.nav-item:hover {
  background: #2d2d2d;
  color: #ffffff;
}

.nav-item.active {
  background: #3b82f6;
  color: #ffffff;
}

.nav-item.active .nav-icon {
  color: #ffffff;
}

.nav-icon {
  color: #9ca3af;
  transition: color 0.15s ease;
}

.nav-item:hover .nav-icon {
  color: #ffffff;
}

.nav-label {
  flex: 1;
}

.nav-count {
  background: #374151;
  color: #d1d5db;
  font-size: 11px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

.nav-item.active .nav-count {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

/* Status List */
.status-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 16px;
  font-size: 13px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-label {
  flex: 1;
  color: #d1d5db;
}

.status-count {
  color: #9ca3af;
  font-size: 12px;
}

/* Quick Actions */
.quick-actions {
  padding: 0 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.quick-action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #2d2d2d;
  border: 1px solid #374151;
  border-radius: 6px;
  color: #d1d5db;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.15s ease;
}

.quick-action-btn:hover {
  background: #374151;
  border-color: #4b5563;
  color: #ffffff;
}

/* Sidebar Footer */
.sidebar-footer {
  padding: 16px;
  border-top: 1px solid #2d2d2d;
}

.project-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
}

.stat-item {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  font-size: 12px;
  color: #9ca3af;
}

.stat-item svg {
  color: #6b7280;
}

/* Scrollbar styling now handled globally in App.css */

/* Responsive */
@media (max-width: 768px) {
  .project-sidebar {
    width: 240px;
  }

  .project-name {
    font-size: 14px;
  }

  .nav-item {
    padding: 6px 12px;
    font-size: 13px;
  }
}

/* Build & Run Button Styles */
.build-run-btn {
  background: #059669 !important;
  border-color: #047857 !important;
  color: #ffffff !important;
  font-weight: 500;
}

.build-run-btn:hover:not(:disabled) {
  background: #047857 !important;
  border-color: #065f46 !important;
}

.build-run-btn.building {
  background: #f59e0b !important;
  border-color: #d97706 !important;
  cursor: not-allowed;
}

.build-run-btn.running {
  background: #dc2626 !important;
  border-color: #b91c1c !important;
}

.build-run-btn.running:hover {
  background: #b91c1c !important;
  border-color: #991b1b !important;
}

.build-run-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Application Access Styles */
.app-access {
  background: #065f46;
  border-top: 1px solid #059669;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  margin-bottom: 1px;
}

.app-access-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #047857;
  border-bottom: 1px solid #059669;
  font-size: 12px;
  font-weight: 500;
  color: #d1fae5;
}

.app-access-content {
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.app-url-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.app-url-label {
  font-size: 11px;
  color: #a7f3d0;
  font-weight: 500;
}

.app-url-link {
  color: #6ee7b7;
  text-decoration: none;
  font-size: 12px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  padding: 4px 8px;
  background: #064e3b;
  border-radius: 4px;
  border: 1px solid #059669;
  transition: all 0.2s ease;
  word-break: break-all;
}

.app-url-link:hover {
  background: #047857;
  color: #d1fae5;
  border-color: #10b981;
}

.app-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
  color: #a7f3d0;
}

.app-port {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.app-status {
  color: #6ee7b7;
  font-weight: 500;
}

/* Build Output Styles */
.build-output {
  background: #111827;
  border-top: 1px solid #374151;
  overflow: hidden;
  max-height: 200px;
  display: flex;
  flex-direction: column;
}

.build-output-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #1f2937;
  border-bottom: 1px solid #374151;
  font-size: 12px;
  font-weight: 500;
  color: #d1d5db;
}

.clear-output-btn {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  font-size: 16px;
  line-height: 1;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 3px;
  transition: all 0.15s ease;
}

.clear-output-btn:hover {
  background: #374151;
  color: #ffffff;
}

.build-output-content {
  padding: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 11px;
  line-height: 1.4;
  color: #e5e7eb;
  background: #111827;
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-y: auto;
  flex: 1;
}

.build-output-content::-webkit-scrollbar {
  width: 6px;
}

.build-output-content::-webkit-scrollbar-track {
  background: #1f2937;
}

.build-output-content::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 3px;
}

.build-output-content::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}
