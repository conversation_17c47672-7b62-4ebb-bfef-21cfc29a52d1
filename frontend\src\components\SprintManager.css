.sprint-manager {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #1a1a1a;
  color: #e5e5e5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.sprint-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #2d2d2d;
  flex-shrink: 0;
}

.sprint-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
}

.sprint-header .btn-primary {
  flex: none;
  width: auto;
}

/* Button Styling to match Stories table */
.btn-primary {
  padding: 8px 16px;
  background: #3b82f6;
  border: 1px solid #3b82f6;
  border-radius: 6px;
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
}

.btn-primary:hover {
  background: #2563eb;
  border-color: #2563eb;
}

.btn-primary.btn-compact {
  padding: 6px 12px;
  font-size: 13px;
  border-radius: 6px;
  flex-shrink: 0;
  white-space: nowrap;
  width: auto;
  max-width: fit-content;
}

.sprint-content {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.sprints-list {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}

.sprint-header-row {
  display: grid;
  grid-template-columns: 40px 1fr 100px 240px 240px 240px 120px;
  align-items: center;
  gap: 16px;
  padding: 12px 24px;
  background: #262626;
  border-bottom: 2px solid #374151;
  font-size: 12px;
  font-weight: 600;
  color: #9ca3af;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.sprint-header-cell {
  text-align: center;
}

.sprint-header-cell:first-child {
  text-align: left;
}

.sprint-header-cell:nth-child(2) {
  text-align: left;
}

.sprint-row {
  border-bottom: 1px solid #2d2d2d;
  transition: all 0.15s ease;
  background: #1a1a1a;
}

.sprint-row:hover {
  background: #262626;
}

.sprint-row:not(:last-child) {
  margin-bottom: 1px;
}

.sprint-main {
  display: grid;
  grid-template-columns: 40px 1fr 100px 240px 240px 240px 120px;
  align-items: center;
  gap: 16px;
  padding: 16px 24px;
  cursor: pointer;
  min-height: 60px;
}

.sprint-left {
  display: contents;
}

.expand-btn {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.15s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.expand-btn:hover {
  background: #374151;
  color: #ffffff;
}

.sprint-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 0; /* Allow text to truncate */
}

.sprint-name {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sprint-dates {
  color: #9ca3af;
  font-size: 12px;
  line-height: 1.3;
  white-space: nowrap;
}

.sprint-right {
  display: contents;
}

.sprint-status-badge {
  display: flex;
  justify-content: center;
}

.status-indicator {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  text-transform: capitalize;
}

.sprint-metrics {
  display: contents;
}

.metric {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  text-align: center;
}

.metric-label {
  color: #9ca3af;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metric-value {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #ffffff;
  font-size: 13px;
  font-weight: 500;
}

.metric-value svg {
  color: #9ca3af;
}

.status-planning {
  background: #1e3a8a;
  color: #60a5fa;
}

.status-active {
  background: #065f46;
  color: #10b981;
}

.status-completed {
  background: #581c87;
  color: #a855f7;
}

.status-default {
  background: #374151;
  color: #9ca3af;
}

.sprint-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
  align-items: center;
}

.sprint-expanded {
  padding: 0 24px 20px 56px;
  border-top: 1px solid #2d2d2d;
  background: #1a1a1a;
}

.sprint-goals {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin: 16px 0;
  color: #9ca3af;
  font-size: 13px;
  line-height: 1.4;
}

.sprint-goals span {
  white-space: pre-line;
}

.sprint-goals svg {
  color: #6b7280;
  flex-shrink: 0;
  margin-top: 1px;
}

.sprint-stories-expanded h4 {
  margin: 16px 0 12px 0;
  color: #d1d5db;
  font-size: 14px;
  font-weight: 600;
}

.story-list {
  display: flex;
  flex-direction: column;
  gap: 0; /* align with table rows, no vertical gaps */
}

.story-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  min-height: 48px;
  background: transparent;
  border: none;
  border-bottom: 1px solid #2d2d2d;
  border-radius: 0;
  transition: all 0.15s ease;
  gap: 12px; /* reduced gap for more compact layout */
  cursor: pointer;
}

.story-item:hover {
  background: #262626; /* match Stories hover */
}

.story-item.draggable {
  cursor: grab;
}

.story-item.draggable:active {
  cursor: grabbing;
}

.story-item.selected {
  background: #1e3a8a; /* match Stories selected */
}

.story-item:hover:not(.selected) {
  background: #262626;
}

.story-drag-handle {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  cursor: grab;
  width: 32px; /* match Stories table drag column width */
  flex-shrink: 0;
}

.story-drag-handle:hover {
  color: #9ca3af;
}

.story-drag-handle:active {
  cursor: grabbing;
}

.drag-indicator {
  font-size: 12px;
  line-height: 1;
}



.story-item .story-title {
  flex: 2; /* take more space relative to other elements */
  font-size: 14px;
  color: #ffffff;
  font-weight: 500;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0; /* allow flex shrinking */
  width: auto !important; /* override StoriesTable.css width constraint */
  max-width: none !important; /* override StoriesTable.css max-width constraint */
}

.story-item .story-meta {
  display: flex;
  align-items: center;
  gap: 6px; /* reduced gap for more compact layout */
  flex-shrink: 1; /* allow shrinking */
  flex-basis: auto; /* size based on content */
  width: fit-content; /* shrink to fit contents */
}

.empty-stories {
  padding: 20px;
  text-align: center;
  color: #9ca3af;
  font-size: 14px;
  font-style: italic;
}

/* Drop target styling */
.sprint-row.drop-target {
  background: rgba(59, 130, 246, 0.1);
  border-color: #3b82f6;
}

.sprint-row.drop-target .sprint-main {
  background: rgba(59, 130, 246, 0.05);
}

.unassigned-stories.drop-target {
  background: rgba(59, 130, 246, 0.1);
  border-color: #3b82f6;
}

/* Empty unassigned area styling */
.empty-unassigned {
  padding: 24px;
  text-align: center;
  color: #6b7280;
  font-size: 14px;
  border: 2px dashed #374151;
  border-radius: 8px;
  background: #1f2937;
  margin-top: 8px;
  transition: all 0.15s ease;
}

.unassigned-stories.drop-target .empty-unassigned {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
  color: #93c5fd;
}

.story-item .story-points {
  background: #3b82f6;
  color: #ffffff;
  width: 20px;
  height: 20px;
  aspect-ratio: 1 / 1;
  border-radius: 50%;
  display: inline-block;
  line-height: 20px;
  text-align: center;
  font-size: 11px;
  font-weight: 600;
  vertical-align: middle;
  box-sizing: border-box;
  padding: 0;
}

/* Match Stories table implementation badges */
.implementation-badge {
  display: flex;
  align-items: center;
  gap: 3px;
  padding: 3px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 500;
}

.implementation-badge.implemented { background:#065f46; color:#10b981; }
.implementation-badge.modified { background:#92400e; color:#fbbf24; }
.implementation-badge.not-started { background:#374151; color:#9ca3af; }

.implementation-status.not_started {
  background: #374151;
  color: #9ca3af;
}

.implementation-status.implemented {
  background: #065f46;
  color: #10b981;
}

.implementation-status.modified_after_implementation {
  background: #92400e;
  color: #fbbf24;
}

.needs-reimplementation {
  background: #7f1d1d;
  color: #fca5a5;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
}

.unassigned-stories {
  margin-top: 32px;
  padding: 24px;
  background: #262626;
  border-top: 2px solid #374151;
  border-radius: 8px 8px 0 0;
  flex-shrink: 0;
}

.unassigned-stories h3 {
  margin: 0 0 16px 0;
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.unassigned-stories h3::before {
  content: '';
  width: 4px;
  height: 16px;
  background: #6b7280;
  border-radius: 2px;
}

/* Button Styles */
.btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
  border: 1px solid;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.btn-small {
  padding: 6px 12px;
  font-size: 12px;
}

.action-icon {
  width: 30px;
  height: 30px;
  cursor: pointer;
  transition: all 0.15s ease;
  border-radius: 4px;
  padding: 4px;
}

.action-icon:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}

.action-icon-secondary {
  color: #9ca3af;
}

.action-icon-secondary:hover {
  color: #d1d5db;
}

.action-icon-success {
  color: #10b981;
}

.action-icon-success:hover {
  color: #34d399;
}

.action-icon-primary {
  color: #3b82f6;
}

.action-icon-primary:hover {
  color: #60a5fa;
}

.action-icon-warning {
  color: #f59e0b;
}

.action-icon-warning:hover {
  color: #fbbf24;
}

.action-icon.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-icon.disabled:hover {
  background: none;
  transform: none;
}

.btn-primary {
  background: #3b82f6;
  border-color: #3b82f6;
  color: #ffffff;
}

.btn-primary:hover {
  background: #2563eb;
  border-color: #2563eb;
}

.btn-success {
  background: #10b981;
  color: #ffffff;
  border-color: #10b981;
}

.btn-success:hover {
  background: #059669;
  border-color: #059669;
}

.btn-warning {
  background: #f59e0b;
  color: #ffffff;
  border-color: #f59e0b;
}

.btn-warning:hover {
  background: #d97706;
  border-color: #d97706;
}

.btn-secondary {
  background: #2d2d2d;
  border-color: #374151;
  color: #d1d5db;
}

.btn-secondary:hover {
  background: #374151;
  border-color: #4b5563;
  color: #ffffff;
}

/* Sprint Modal Styles */
.sprint-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.sprint-modal {
  background: #1a1a1a;
  border: 1px solid #374151;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px rgba(0, 0, 0, 0.3);
}

.sprint-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #2d2d2d;
}

.sprint-modal-header h2 {
  margin: 0;
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
}

.sprint-modal-close {
  background: none;
  border: none;
  font-size: 20px;
  color: #9ca3af;
  cursor: pointer;
  padding: 4px;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.15s ease;
}

.sprint-modal-close:hover {
  background: #374151;
  color: #ffffff;
}

.sprint-modal-content {
  padding: 20px;
}

/* Sprint Modal Form Styles */
.sprint-form-group {
  margin-bottom: 16px;
}

.sprint-form-group label {
  display: block;
  margin-bottom: 6px;
  color: #d1d5db;
  font-weight: 500;
  font-size: 14px;
}

.sprint-form-group input,
.sprint-form-group textarea {
  width: 100%;
  padding: 10px 12px;
  background: #2d2d2d;
  border: 1px solid #374151;
  border-radius: 6px;
  color: #ffffff;
  font-size: 14px;
  transition: border-color 0.15s ease;
  box-sizing: border-box;
}

.sprint-form-group input:focus,
.sprint-form-group textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.sprint-form-group textarea {
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}

.sprint-form-group input::placeholder,
.sprint-form-group textarea::placeholder {
  color: #6b7280;
}

.sprint-form-group input[type="date"] {
  color-scheme: dark;
}

.sprint-form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.sprint-modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #2d2d2d;
}

/* Scrollbar styling now handled globally in App.css */

/* Animation for expand/collapse */
.sprint-expanded {
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 500px;
  }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .sprint-main {
    grid-template-columns: 40px 1fr 90px 180px 180px 180px 100px;
    gap: 12px;
  }

  .sprint-header-row {
    grid-template-columns: 40px 1fr 90px 180px 180px 180px 100px;
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .sprint-header {
    padding: 16px 20px;
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .sprint-header h2 {
    font-size: 18px;
  }

  .sprint-header .btn-primary {
    align-self: flex-start;
  }

  .sprint-header-row {
    display: none;
  }



  .sprint-main {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    padding: 12px 20px;
  }

  .sprint-left {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .sprint-right {
    display: flex;
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .sprint-metrics {
    display: flex;
    justify-content: space-around;
    gap: 12px;
  }

  .metric {
    flex: 1;
    text-align: center;
  }

  .sprint-actions {
    display: flex;
    justify-content: center;
    gap: 8px;
  }

  .unassigned-stories {
    margin-top: 16px;
    padding: 16px;
  }

  .sprint-form-row {
    grid-template-columns: 1fr;
  }

  .sprint-modal {
    width: 95%;
    margin: 20px;
  }

  .sprint-modal-header,
  .sprint-modal-content {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .sprint-header {
    padding: 12px 16px;
  }

  .sprint-header .btn-primary {
    align-self: flex-start;
  }

  .sprint-main {
    padding: 12px 16px;
  }

  .story-item {
    gap: 8px; /* tighter spacing on mobile */
  }

  .sprint-metrics {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .metric {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    padding: 4px 8px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
  }

  .unassigned-stories {
    padding: 12px;
  }
}
