import { useState, useEffect } from 'react';
import { format, addMonths, addWeeks, startOfMonth, endOfMonth, startOfWeek, endOfWeek, eachDayOfInterval, differenceInDays, parseISO } from 'date-fns';
import './GanttChart.css';

const GanttChart = ({ projects = [], onProjectClick }) => {
  const [viewMode, setViewMode] = useState('Month');
  const [selectedProject, setSelectedProject] = useState(null);

  // Generate timeline based on view mode
  const generateTimeline = () => {
    if (viewMode === 'Week') {
      // Generate 26 weeks starting from January 2025
      const start = startOfWeek(new Date(2025, 0, 1)); // Start of week containing Jan 1, 2025
      const weeks = [];
      for (let i = 0; i < 26; i++) {
        weeks.push(addWeeks(start, i));
      }
      return weeks;
    } else {
      // Generate 12 months starting from January 2025
      const start = new Date(2025, 0, 1); // January 1, 2025
      const months = [];
      for (let i = 0; i < 12; i++) {
        months.push(addMonths(start, i));
      }
      return months;
    }
  };

  const timeline = generateTimeline();

  // Calculate project bar position and width
  const getProjectPosition = (project) => {
    if (!project.start_date || !project.end_date) return { left: 0, width: 0 };

    const startDate = parseISO(project.start_date);
    const endDate = parseISO(project.end_date);
    const timelineStart = timeline[0];
    const timelineEnd = timeline[timeline.length - 1];

    // Calculate position as percentage of timeline
    let totalDays, timelineStartDate, timelineEndDate;

    if (viewMode === 'Week') {
      timelineStartDate = startOfWeek(timelineStart);
      timelineEndDate = endOfWeek(timelineEnd);
    } else {
      timelineStartDate = startOfMonth(timelineStart);
      timelineEndDate = endOfMonth(timelineEnd);
    }

    totalDays = differenceInDays(timelineEndDate, timelineStartDate);
    const startOffset = differenceInDays(startDate, timelineStartDate);
    const duration = differenceInDays(endDate, startDate);

    const left = Math.max(0, (startOffset / totalDays) * 100);
    const width = Math.min(100 - left, (duration / totalDays) * 100);

    return { left, width };
  };

  function getStatusColor(status) {
    const colors = {
      planning: '#6366f1',
      active: '#10b981',
      on_hold: '#f59e0b',
      completed: '#22c55e',
      cancelled: '#ef4444'
    };
    return colors[status] || '#6b7280';
  }

  if (projects.length === 0) {
    return (
      <div className="gantt-empty">
        <p>No projects to display in timeline</p>
      </div>
    );
  }

  return (
    <div className="gantt-chart">
      <div className="gantt-controls">
        <select
          value={viewMode}
          onChange={(e) => setViewMode(e.target.value)}
          className="view-mode-select"
        >
          <option value="Week">Week View</option>
          <option value="Month">Month View</option>
        </select>
      </div>

      <div className="gantt-container">
        {/* Timeline Header */}
        <div className="gantt-timeline-header">
          <div className="gantt-project-column">Project</div>
          <div className="gantt-timeline-months">
            {timeline.map((period, index) => (
              <div key={index} className="gantt-month">
                {viewMode === 'Week'
                  ? format(period, 'MMM dd')
                  : format(period, 'MMM yyyy')
                }
              </div>
            ))}
          </div>
        </div>

        {/* Project Rows */}
        <div className="gantt-body">
          {projects.map((project) => {
            const position = getProjectPosition(project);
            return (
              <div key={project.id} className="gantt-row">
                <div className="gantt-project-info">
                  <div className="project-name">{project.name}</div>
                  <div className="project-dates">
                    {project.start_date && format(parseISO(project.start_date), 'MMM dd')} -
                    {project.end_date && format(parseISO(project.end_date), 'MMM dd')}
                  </div>
                </div>
                <div className="gantt-timeline-row">
                  <div
                    className="gantt-bar"
                    style={{
                      left: `${position.left}%`,
                      width: `${position.width}%`,
                      backgroundColor: getStatusColor(project.status)
                    }}
                    onClick={() => setSelectedProject(project)}
                  >
                    <span className="gantt-bar-text">{project.progress || 0}%</span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Project Modal */}
      {selectedProject && (
        <div className="modal-overlay" onClick={() => setSelectedProject(null)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>{selectedProject.name}</h3>
              <button
                className="modal-close"
                onClick={() => setSelectedProject(null)}
              >
                ×
              </button>
            </div>

            <div className="modal-body">
              <div className="project-status">
                <span className={`status-badge status-${selectedProject.status}`}>
                  {selectedProject.status}
                </span>
              </div>

              <p className="project-description">
                {selectedProject.description}
              </p>

              <div className="progress-section">
                <div className="progress-header">
                  <span className="progress-label">Progress</span>
                  <span className="progress-percentage">{selectedProject.progress || 0}%</span>
                </div>
                <div className="progress-bar-container">
                  <div
                    className="progress-bar-fill"
                    style={{
                      width: `${selectedProject.progress || 0}%`,
                      backgroundColor: getStatusColor(selectedProject.status)
                    }}
                  ></div>
                </div>
              </div>

              <div className="project-details">
                <div className="detail-row">
                  <span className="detail-label">Start Date:</span>
                  <span className="detail-value">
                    {selectedProject.start_date && format(parseISO(selectedProject.start_date), 'MMM dd, yyyy')}
                  </span>
                </div>
                <div className="detail-row">
                  <span className="detail-label">End Date:</span>
                  <span className="detail-value">
                    {selectedProject.end_date && format(parseISO(selectedProject.end_date), 'MMM dd, yyyy')}
                  </span>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button
                className="btn-secondary"
                onClick={() => setSelectedProject(null)}
              >
                Close
              </button>
              <button
                className="btn-primary"
                onClick={() => {
                  if (onProjectClick) {
                    onProjectClick(selectedProject);
                  }
                  setSelectedProject(null);
                }}
              >
                Open Project
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GanttChart;
