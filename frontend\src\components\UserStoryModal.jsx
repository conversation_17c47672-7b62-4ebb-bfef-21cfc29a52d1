import { useState, useEffect } from 'react';
import { X, User, Target, FileText, AlertCircle, MessageCircle, Edit3 } from 'lucide-react';
import CommentTimeline from './CommentTimeline';
import AIProgressModal from './AIProgressModal';
import './UserStoryModal.css';

const UserStoryModal = ({ isOpen, onClose, onSave, story = null, projectId }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    acceptance_criteria: '',
    priority: 'medium',
    story_points: 3,
    epic: '',
    user_persona: 'user',
    business_value: ''
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [activeTab, setActiveTab] = useState('form');
  const [showAIProgressModal, setShowAIProgressModal] = useState(false);

  useEffect(() => {
    if (story) {
      setFormData({
        title: story.title || '',
        description: story.description || '',
        acceptance_criteria: story.acceptance_criteria || '',
        priority: story.priority || 'medium',
        story_points: story.story_points || 3,
        epic: story.epic || '',
        user_persona: story.user_persona || 'user',
        business_value: story.business_value || ''
      });
    } else {
      setFormData({
        title: '',
        description: '',
        acceptance_criteria: '',
        priority: 'medium',
        story_points: 3,
        epic: '',
        user_persona: 'user',
        business_value: ''
      });
    }
    setErrors({});
  }, [story, isOpen]);

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.title.trim()) {
      newErrors.title = 'Story title is required';
    }
    
    if (!formData.description.trim()) {
      newErrors.description = 'Story description is required';
    }
    
    if (!formData.acceptance_criteria.trim()) {
      newErrors.acceptance_criteria = 'Acceptance criteria are required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    
    try {
      const storyData = {
        ...formData,
        project_id: projectId,
        status: 'backlog'
      };

      const url = story 
        ? `http://localhost:3000/projects/${projectId}/stories/${story.id}`
        : `http://localhost:3000/projects/${projectId}/stories`;
      
      const method = story ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(storyData),
      });

      if (response.ok) {
        const result = await response.json();
        onSave(result);
        onClose();
      } else {
        const errorData = await response.json();
        setErrors({ submit: errorData.error || 'Failed to save user story' });
      }
    } catch (error) {
      console.error('Error saving user story:', error);
      setErrors({ submit: 'Failed to save user story' });
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="story-modal-overlay">
      <div className="story-modal">
        <div className="story-modal-header">
          <h2>
            <FileText size={24} />
            {story ? 'Edit User Story' : 'Create User Story'}
          </h2>
          <button className="story-modal-close" onClick={onClose}>
            <X size={24} />
          </button>
        </div>

        {/* Tab Navigation - only show for existing stories */}
        {story && (
          <div className="story-modal-tabs">
            <button
              className={`tab-button ${activeTab === 'form' ? 'active' : ''}`}
              onClick={() => setActiveTab('form')}
            >
              <Edit3 size={16} />
              Edit Story
            </button>
            <button
              className={`tab-button ${activeTab === 'progress' ? 'active' : ''}`}
              onClick={() => setActiveTab('progress')}
            >
              <MessageCircle size={16} />
              AI Progress
            </button>
          </div>
        )}
        
        <div className="story-modal-content">
          {/* Form Tab */}
          {activeTab === 'form' && (
            <form onSubmit={handleSubmit}>
            <div className="story-form-group">
            <label htmlFor="title">Story Title *</label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleChange}
              className={errors.title ? 'error' : ''}
              placeholder="As a [user], I want [goal] so that [benefit]"
            />
            {errors.title && <span className="error-message">{errors.title}</span>}
          </div>

          <div className="story-form-row">
            <div className="story-form-group">
              <label htmlFor="user_persona">User Persona</label>
              <select
                id="user_persona"
                name="user_persona"
                value={formData.user_persona}
                onChange={handleChange}
              >
                <option value="user">End User</option>
                <option value="admin">Administrator</option>
                <option value="customer">Customer</option>
                <option value="developer">Developer</option>
                <option value="manager">Manager</option>
              </select>
            </div>

            <div className="story-form-group">
              <label htmlFor="epic">Epic (Optional)</label>
              <input
                type="text"
                id="epic"
                name="epic"
                value={formData.epic}
                onChange={handleChange}
                placeholder="e.g., User Authentication"
              />
            </div>
          </div>
          
          <div className="story-form-group">
            <label htmlFor="description">Description *</label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              className={errors.description ? 'error' : ''}
              placeholder="Detailed description of what the user wants to accomplish..."
              rows={4}
            />
            {errors.description && <span className="error-message">{errors.description}</span>}
          </div>
          
          <div className="story-form-group">
            <label htmlFor="acceptance_criteria">Acceptance Criteria *</label>
            <textarea
              id="acceptance_criteria"
              name="acceptance_criteria"
              value={formData.acceptance_criteria}
              onChange={handleChange}
              className={errors.acceptance_criteria ? 'error' : ''}
              placeholder="Given [context], When [action], Then [outcome]..."
              rows={4}
            />
            {errors.acceptance_criteria && <span className="error-message">{errors.acceptance_criteria}</span>}
          </div>

          <div className="story-form-group">
            <label htmlFor="business_value">Business Value</label>
            <textarea
              id="business_value"
              name="business_value"
              value={formData.business_value}
              onChange={handleChange}
              placeholder="Why is this story important? What business value does it provide?"
              rows={2}
            />
          </div>
          
          <div className="story-form-row">
            <div className="story-form-group">
              <label htmlFor="priority">Priority</label>
              <select
                id="priority"
                name="priority"
                value={formData.priority}
                onChange={handleChange}
              >
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
                <option value="critical">Critical</option>
              </select>
            </div>

            <div className="story-form-group">
              <label htmlFor="story_points">Story Points</label>
              <select
                id="story_points"
                name="story_points"
                value={formData.story_points}
                onChange={handleChange}
              >
                <option value="1">1 - Very Small</option>
                <option value="2">2 - Small</option>
                <option value="3">3 - Medium</option>
                <option value="5">5 - Large</option>
                <option value="8">8 - Very Large</option>
                <option value="13">13 - Extra Large</option>
              </select>
            </div>
          </div>

          {errors.submit && (
            <div className="error-banner">
              <AlertCircle size={16} />
              {errors.submit}
            </div>
          )}
          
              <div className="story-modal-actions">
                <button type="button" className="btn-secondary" onClick={onClose}>
                  Cancel
                </button>
                <button type="submit" className="btn-primary" disabled={loading}>
                  {loading ? 'Saving...' : (story ? 'Update Story' : 'Create Story')}
                </button>
              </div>
            </form>
          )}

          {/* AI Progress Tab */}
          {activeTab === 'progress' && story && (
            <div className="progress-tab-content">
              <div className="progress-header">
                <h3>AI Development Progress</h3>
                <p>Track AI agent activities and decisions for this user story</p>
              </div>

              <div className="progress-preview">
                <div className="preview-message">
                  <MessageCircle size={48} />
                  <h4>View AI Progress in Detail</h4>
                  <p>Open the full AI progress viewer to see detailed comments, decisions, and timeline in a dedicated interface with better readability.</p>

                  <button
                    type="button"
                    className="btn-primary open-progress-btn"
                    onClick={() => setShowAIProgressModal(true)}
                  >
                    <MessageCircle size={16} />
                    Open AI Progress Viewer
                  </button>
                </div>
              </div>

              <div className="progress-actions">
                <button type="button" className="btn-secondary" onClick={onClose}>
                  Close
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* AI Progress Modal */}
      <AIProgressModal
        isOpen={showAIProgressModal}
        onClose={() => setShowAIProgressModal(false)}
        projectId={projectId}
        storyId={story?.id}
        storyTitle={story?.title}
      />
    </div>
  );
};

export default UserStoryModal;
