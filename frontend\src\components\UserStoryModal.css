/* User Story Modal Styles */
.story-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.story-modal {
  background: #1a1a1a;
  border: 1px solid #374151;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px rgba(0, 0, 0, 0.3);
}

.story-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #2d2d2d;
}

.story-modal-header h2 {
  margin: 0;
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.story-modal-close {
  background: none;
  border: none;
  font-size: 20px;
  color: #9ca3af;
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.15s ease;
}

.story-modal-close:hover {
  background: #374151;
  color: #ffffff;
}

.story-modal-tabs {
  display: flex;
  border-bottom: 1px solid #2d2d2d;
  background: #1a1a1a;
}

.tab-button {
  background: none;
  border: none;
  padding: 12px 20px;
  color: #9ca3af;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  border-bottom: 2px solid transparent;
  transition: all 0.15s ease;
}

.tab-button:hover {
  color: #d1d5db;
  background: #2d2d2d;
}

.tab-button.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
  background: #1e293b;
}

.story-modal-content {
  padding: 20px;
}

.progress-tab-content {
  min-height: 400px;
}

.progress-header {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #2d2d2d;
}

.progress-header h3 {
  margin: 0 0 8px 0;
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
}

.progress-header p {
  margin: 0;
  color: #9ca3af;
  font-size: 14px;
}

.progress-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  padding: 40px 20px;
}

.preview-message {
  text-align: center;
  max-width: 400px;
  color: #9ca3af;
}

.preview-message svg {
  margin-bottom: 16px;
  opacity: 0.6;
}

.preview-message h4 {
  margin: 0 0 12px 0;
  color: #d1d5db;
  font-size: 18px;
  font-weight: 600;
}

.preview-message p {
  margin: 0 0 24px 0;
  line-height: 1.5;
  font-size: 14px;
}

.open-progress-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 auto;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
}

.progress-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #2d2d2d;
}

/* Story Modal Form Styles */
.story-form-group {
  margin-bottom: 16px;
}

.story-form-group label {
  display: block;
  margin-bottom: 6px;
  color: #d1d5db;
  font-weight: 500;
  font-size: 14px;
}

.story-form-group input,
.story-form-group select,
.story-form-group textarea {
  width: 100%;
  padding: 10px 12px;
  background: #2d2d2d;
  border: 1px solid #374151;
  border-radius: 6px;
  color: #ffffff;
  font-size: 14px;
  transition: border-color 0.15s ease;
  box-sizing: border-box;
}

.story-form-group input:focus,
.story-form-group select:focus,
.story-form-group textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.story-form-group input.error,
.story-form-group select.error,
.story-form-group textarea.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.story-form-group textarea {
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}

.story-form-group input::placeholder,
.story-form-group select::placeholder,
.story-form-group textarea::placeholder {
  color: #6b7280;
}

.story-form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.error-message {
  color: #ef4444;
  font-size: 12px;
  margin-top: 4px;
  display: block;
}

.submit-error {
  background: #2d1b1b;
  border: 1px solid #5f2c2c;
  color: #ef4444;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.story-modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #2d2d2d;
}

/* Button Styles */
.btn-primary {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-secondary {
  background: #374151;
  color: #d1d5db;
  border: 1px solid #4b5563;
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
}

.btn-secondary:hover {
  background: #4b5563;
  border-color: #6b7280;
  color: #ffffff;
}

/* Responsive Design */
@media (max-width: 768px) {
  .story-modal {
    width: 95%;
    margin: 20px;
  }

  .story-modal-header,
  .story-modal-content {
    padding: 16px;
  }

  .story-form-row {
    grid-template-columns: 1fr;
  }

  .story-modal-actions {
    flex-direction: column-reverse;
  }

  .story-modal-actions button {
    width: 100%;
  }
}

/* Story Priority Colors */
.priority-low {
  color: #10b981;
}

.priority-medium {
  color: #f59e0b;
}

.priority-high {
  color: #ef4444;
}

.priority-critical {
  color: #dc2626;
  font-weight: 700;
}

/* Story Points Styling */
.story-points {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  font-size: 0.75rem;
  font-weight: 600;
}
