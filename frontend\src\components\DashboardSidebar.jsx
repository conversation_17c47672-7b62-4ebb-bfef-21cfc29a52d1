import React from 'react';
import {
  Home,
  FolderOpen,
  Calendar,
  BarChart3,
  Settings,
  Users,
  Target,
  TrendingUp,
  Clock,
  CheckCircle,
  Plus,
  Bot
} from 'lucide-react';
import './DashboardSidebar.css';

const DashboardSidebar = ({ activeView, onViewChange, stats, onCreateProject }) => {
  const menuItems = [
    {
      id: 'overview',
      label: 'Overview',
      icon: Home,
      count: null
    },
    {
      id: 'projects',
      label: 'Projects',
      icon: FolderOpen,
      count: stats?.total || 0
    },
    {
      id: 'timeline',
      label: 'Timeline',
      icon: Calendar,
      count: null
    },
    {
      id: 'reports',
      label: 'Reports',
      icon: BarChart3,
      count: null
    },
    {
      id: 'ai-scheduler',
      label: 'AI Scheduler',
      icon: Bot,
      count: null
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: Settings,
      count: null
    }
  ];

  const statusItems = [
    {
      label: 'Active',
      count: stats?.active || 0,
      color: '#10b981',
      icon: Clock
    },
    {
      label: 'Completed',
      count: stats?.completed || 0,
      color: '#22c55e',
      icon: CheckCircle
    },
    {
      label: 'Planning',
      count: stats?.planning || 0,
      color: '#6366f1',
      icon: Target
    }
  ];

  return (
    <div className="dashboard-sidebar">
      {/* Header */}
      <div className="sidebar-header">
        <div className="brand-logo">
          <div className="logo-icon">Q</div>
          <h1>Quarrel</h1>
        </div>
        <p className="brand-subtitle">Project Management</p>
      </div>

      {/* Create Project Button */}
      <button 
        className="create-project-sidebar-btn"
        onClick={onCreateProject}
      >
        <Plus size={16} />
        New Project
      </button>

      {/* Navigation Menu */}
      <nav className="sidebar-nav">
        <div className="nav-section">
          <div className="nav-section-header">Navigation</div>
          <ul className="nav-list">
            {menuItems.map((item) => {
              const IconComponent = item.icon;
              return (
                <li key={item.id}>
                  <button
                    className={`nav-item ${activeView === item.id ? 'active' : ''}`}
                    onClick={() => onViewChange(item.id)}
                  >
                    <IconComponent className="nav-icon" size={18} />
                    <span>{item.label}</span>
                    {item.count !== null && (
                      <span className="nav-count">{item.count}</span>
                    )}
                  </button>
                </li>
              );
            })}
          </ul>
        </div>

        {/* Project Status */}
        <div className="nav-section">
          <div className="nav-section-header">Project Status</div>
          <ul className="status-list">
            {statusItems.map((item, index) => {
              const IconComponent = item.icon;
              return (
                <li key={index} className="status-item">
                  <div className="status-icon" style={{ color: item.color }}>
                    <IconComponent size={16} />
                  </div>
                  <div className="status-content">
                    <span className="status-label">{item.label}</span>
                    <span className="status-count">{item.count}</span>
                  </div>
                </li>
              );
            })}
          </ul>
        </div>
      </nav>
    </div>
  );
};

export default DashboardSidebar;
