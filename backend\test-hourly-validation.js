const { AIProcessingScheduler } = require('./ai-scheduler');
const { AcceptanceCriteriaValidator } = require('./acceptance-criteria-validator');
const db = require('./database');
const { nanoid } = require('nanoid');

async function testHourlyValidation() {
  console.log('🧪 Testing Hourly Validation Functionality...\n');

  const scheduler = new AIProcessingScheduler();
  const validator = new AcceptanceCriteriaValidator();

  try {
    // Test 1: Create test data
    console.log('1. Creating test data...');
    const testData = await createTestData();
    console.log(`✅ Created project ${testData.projectId} with story ${testData.storyId}`);

    // Test 2: Test AcceptanceCriteriaValidator directly
    console.log('\n2. Testing AcceptanceCriteriaValidator...');
    const validationResult = await validator.validateStoryImplementation(
      testData.story,
      testData.projectId,
      testData.workspacePath
    );
    console.log(`✅ Validation result: ${validationResult.passed ? 'PASSED' : 'FAILED'} (Score: ${validationResult.score}/100)`);
    console.log(`   Feedback: ${validationResult.feedback}`);

    // Test 3: Test scheduler configuration for hourly processing
    console.log('\n3. Testing scheduler configuration...');
    console.log(`   Default tick rate: ${scheduler.config.tickRate}ms (${scheduler.config.tickRate / 1000 / 60} minutes)`);
    console.log(`   Validation enabled: ${scheduler.config.guardrails.enableAcceptanceCriteriaValidation}`);
    console.log(`   Validation threshold: ${scheduler.config.guardrails.validationScoreThreshold}`);

    // Test 4: Test getting stories needing validation
    console.log('\n4. Testing story retrieval for validation...');
    const storiesNeedingValidation = await scheduler.getStoriesNeedingValidation(testData.projectId);
    console.log(`✅ Found ${storiesNeedingValidation.length} stories needing validation`);

    // Test 5: Test validation processing
    console.log('\n5. Testing validation processing...');
    if (storiesNeedingValidation.length > 0) {
      await scheduler.validateStory(testData.projectId, storiesNeedingValidation[0], testData.workspacePath);
      console.log('✅ Validation processing completed');
    }

    // Test 6: Test scheduler status
    console.log('\n6. Testing scheduler status...');
    const status = scheduler.getStatus();
    console.log(`   Running: ${status.isRunning}`);
    console.log(`   Stories validated: ${status.stats.storiesValidated}`);
    console.log(`   Validations passed: ${status.stats.validationsPassed}`);
    console.log(`   Validations failed: ${status.stats.validationsFailed}`);

    console.log('\n✅ All tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    // Cleanup
    console.log('\n🧹 Cleaning up test data...');
    await cleanupTestData();
  }
}

async function createTestData() {
  const projectId = 'test-hourly-' + nanoid();
  const sprintId = 'test-sprint-' + nanoid();
  const storyId = 'test-story-' + nanoid();

  // Create test project
  await new Promise((resolve, reject) => {
    const projectQuery = `INSERT INTO projects (
      id, name, description, status, ai_enabled, ai_processing_enabled, tech_stack, workspace_path
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`;

    db.run(projectQuery, [
      projectId,
      'Test Hourly Validation Project',
      'A test project for hourly validation functionality',
      'active',
      1,
      1,
      'Node.js, React',
      './test-workspace'
    ], function(err) {
      if (err) reject(err);
      else resolve();
    });
  });

  // Create test sprint
  await new Promise((resolve, reject) => {
    const today = new Date();
    const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
    
    const sprintQuery = `INSERT INTO sprints (
      id, project_id, name, description, start_date, end_date, status, ai_processing_enabled
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`;

    db.run(sprintQuery, [
      sprintId,
      projectId,
      'Test Sprint for Validation',
      'A test sprint for validation functionality',
      today.toISOString().split('T')[0],
      nextWeek.toISOString().split('T')[0],
      'active',
      1
    ], function(err) {
      if (err) reject(err);
      else resolve();
    });
  });

  // Create test user story with 'implemented' status
  const story = {
    id: storyId,
    project_id: projectId,
    sprint_id: sprintId,
    title: 'Test User Authentication',
    description: 'As a user, I want to be able to log in to the application so that I can access my account.',
    acceptance_criteria: `
1. User can enter username and password
2. System validates credentials against database
3. Successful login redirects to dashboard
4. Failed login shows error message
5. Login form has proper validation
6. Password field is masked
    `.trim(),
    priority: 'high',
    status: 'in_progress',
    implementation_status: 'implemented'
  };

  await new Promise((resolve, reject) => {
    const storyQuery = `INSERT INTO user_stories (
      id, project_id, sprint_id, title, description, acceptance_criteria, 
      priority, status, implementation_status
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`;

    db.run(storyQuery, [
      story.id,
      story.project_id,
      story.sprint_id,
      story.title,
      story.description,
      story.acceptance_criteria,
      story.priority,
      story.status,
      story.implementation_status
    ], function(err) {
      if (err) reject(err);
      else resolve();
    });
  });

  return {
    projectId,
    sprintId,
    storyId,
    story,
    workspacePath: './test-workspace'
  };
}

async function cleanupTestData() {
  return new Promise((resolve, reject) => {
    // Delete test data (projects will cascade delete stories and sprints)
    const query = `DELETE FROM projects WHERE name LIKE 'Test Hourly Validation%'`;
    
    db.run(query, [], function(err) {
      if (err) {
        console.error('Error cleaning up test data:', err);
        reject(err);
      } else {
        console.log(`✅ Cleaned up ${this.changes} test projects`);
        resolve();
      }
    });
  });
}

// Test configuration scenarios
async function testConfigurationScenarios() {
  console.log('\n🔧 Testing Configuration Scenarios...\n');

  const scheduler = new AIProcessingScheduler();

  // Test 1: Hourly configuration
  console.log('1. Testing hourly configuration...');
  scheduler.updateConfig({
    tickRate: 3600000, // 1 hour
    guardrails: {
      enableAcceptanceCriteriaValidation: true,
      validationScoreThreshold: 70
    }
  });
  console.log(`✅ Tick rate: ${scheduler.config.tickRate / 1000 / 60} minutes`);

  // Test 2: Validation disabled
  console.log('\n2. Testing with validation disabled...');
  scheduler.updateConfig({
    guardrails: {
      enableAcceptanceCriteriaValidation: false
    }
  });
  console.log(`✅ Validation enabled: ${scheduler.config.guardrails.enableAcceptanceCriteriaValidation}`);

  // Test 3: Different validation thresholds
  console.log('\n3. Testing different validation thresholds...');
  const thresholds = [50, 70, 85, 95];
  for (const threshold of thresholds) {
    scheduler.updateConfig({
      guardrails: {
        validationScoreThreshold: threshold
      }
    });
    console.log(`   Threshold ${threshold}: ${scheduler.config.guardrails.validationScoreThreshold}`);
  }

  console.log('\n✅ Configuration tests completed!');
}

// Run tests
async function runAllTests() {
  try {
    await testHourlyValidation();
    await testConfigurationScenarios();
    console.log('\n🎉 All tests passed successfully!');
  } catch (error) {
    console.error('\n💥 Tests failed:', error);
    process.exit(1);
  }
}

// Export for use in other modules
module.exports = {
  testHourlyValidation,
  testConfigurationScenarios,
  runAllTests
};

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests();
}
