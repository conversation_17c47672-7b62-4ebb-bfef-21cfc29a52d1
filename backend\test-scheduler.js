const { AIProcessingScheduler } = require('./ai-scheduler');
const db = require('./database');

async function testScheduler() {
  console.log('🧪 Testing AI Processing Scheduler...\n');

  const scheduler = new AIProcessingScheduler();

  try {
    // Test 1: Check initial status
    console.log('1. Testing initial status...');
    const initialStatus = scheduler.getStatus();
    console.log('✅ Initial status:', {
      isRunning: initialStatus.isRunning,
      tickRate: initialStatus.config.tickRate,
      guardrails: Object.keys(initialStatus.config.guardrails).length
    });

    // Test 2: Create test project and sprint
    console.log('\n2. Creating test project and sprint...');
    await createTestData();
    console.log('✅ Test data created');

    // Test 3: Test configuration update
    console.log('\n3. Testing configuration update...');
    scheduler.updateConfig({
      tickRate: 10000, // 10 seconds for testing
      guardrails: {
        ...scheduler.config.guardrails,
        maxStoriesPerTick: 1
      }
    });
    console.log('✅ Configuration updated');

    // Test 4: Start scheduler
    console.log('\n4. Testing scheduler start...');
    const started = await scheduler.start();
    console.log('✅ Scheduler started:', started);

    // Test 5: Let it run for a few ticks
    console.log('\n5. Running scheduler for 15 seconds...');
    await new Promise(resolve => setTimeout(resolve, 15000));

    // Test 6: Check stats
    console.log('\n6. Checking scheduler stats...');
    const runningStatus = scheduler.getStatus();
    console.log('✅ Running stats:', {
      totalTicks: runningStatus.stats.totalTicks,
      storiesProcessed: runningStatus.stats.storiesProcessed,
      errors: runningStatus.stats.errors,
      skippedTicks: runningStatus.stats.skippedTicks
    });

    // Test 7: Stop scheduler
    console.log('\n7. Testing scheduler stop...');
    const stopped = await scheduler.stop();
    console.log('✅ Scheduler stopped:', stopped);

    // Test 8: Final status
    console.log('\n8. Final status check...');
    const finalStatus = scheduler.getStatus();
    console.log('✅ Final status:', {
      isRunning: finalStatus.isRunning,
      totalTicks: finalStatus.stats.totalTicks,
      storiesProcessed: finalStatus.stats.storiesProcessed
    });

    console.log('\n🎉 All scheduler tests passed!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

async function createTestData() {
  return new Promise((resolve, reject) => {
    // Create test project
    const projectId = 'test-project-' + Date.now();
    const projectQuery = `INSERT INTO projects (
      id, name, description, status, ai_enabled, ai_processing_enabled, tech_stack
    ) VALUES (?, ?, ?, ?, ?, ?, ?)`;

    db.run(projectQuery, [
      projectId,
      'Test AI Scheduler Project',
      'A test project for AI scheduler functionality',
      'active',
      1,
      1,
      'Node.js, React'
    ], function(err) {
      if (err) {
        reject(err);
        return;
      }

      // Create test sprint
      const sprintId = 'test-sprint-' + Date.now();
      const today = new Date();
      const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
      
      const sprintQuery = `INSERT INTO sprints (
        id, project_id, name, description, start_date, end_date, status, ai_processing_enabled
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`;

      db.run(sprintQuery, [
        sprintId,
        projectId,
        'Test Sprint',
        'A test sprint for AI scheduler',
        today.toISOString().split('T')[0],
        nextWeek.toISOString().split('T')[0],
        'active',
        1
      ], function(err) {
        if (err) {
          reject(err);
          return;
        }

        // Create test user stories
        const stories = [
          {
            title: 'Test Story 1',
            description: 'First test story for AI processing',
            priority: 'high'
          },
          {
            title: 'Test Story 2', 
            description: 'Second test story for AI processing',
            priority: 'medium'
          },
          {
            title: 'Test Story 3',
            description: 'Third test story for AI processing',
            priority: 'low'
          }
        ];

        let completed = 0;
        stories.forEach((story, index) => {
          const storyId = `test-story-${Date.now()}-${index}`;
          const storyQuery = `INSERT INTO user_stories (
            id, project_id, sprint_id, title, description, acceptance_criteria, priority, status, implementation_status
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`;

          db.run(storyQuery, [
            storyId,
            projectId,
            sprintId,
            story.title,
            story.description,
            'Test acceptance criteria for ' + story.title,
            story.priority,
            'backlog',
            'not_started'
          ], function(err) {
            if (err) {
              reject(err);
              return;
            }
            
            completed++;
            if (completed === stories.length) {
              console.log(`✅ Created project ${projectId} with sprint ${sprintId} and ${stories.length} stories`);
              resolve();
            }
          });
        });
      });
    });
  });
}

// Test guardrails functionality
async function testGuardrails() {
  console.log('\n🛡️ Testing Guardrails...\n');

  const scheduler = new AIProcessingScheduler();

  try {
    // Test working hours
    console.log('1. Testing working hours guardrail...');
    scheduler.updateConfig({
      workingHours: {
        enabled: true,
        start: '09:00',
        end: '17:00'
      }
    });
    
    const withinHours = scheduler.isWithinWorkingHours();
    console.log('✅ Working hours check:', withinHours);

    // Test project filtering
    console.log('\n2. Testing project filtering...');
    const activeProjects = await scheduler.getActiveProjects();
    console.log('✅ Active projects found:', activeProjects.length);

    // Test sprint engagement rules
    console.log('\n3. Testing sprint engagement rules...');
    if (activeProjects.length > 0) {
      const sprint = await scheduler.getActiveSprint(activeProjects[0].id);
      if (sprint) {
        const shouldEngage = scheduler.shouldEngageWithSprint(sprint);
        console.log('✅ Sprint engagement check:', shouldEngage);
      }
    }

    console.log('\n🎉 All guardrail tests passed!');

  } catch (error) {
    console.error('❌ Guardrail test failed:', error);
  }
}

// Run tests
async function runAllTests() {
  console.log('🚀 Starting AI Scheduler Tests\n');
  console.log('=' .repeat(50));
  
  await testScheduler();
  
  console.log('\n' + '=' .repeat(50));
  
  await testGuardrails();
  
  console.log('\n' + '=' .repeat(50));
  console.log('🏁 All tests completed!');
  
  process.exit(0);
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Test interrupted by user');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Test terminated');
  process.exit(0);
});

if (require.main === module) {
  runAllTests().catch(error => {
    console.error('❌ Test suite failed:', error);
    process.exit(1);
  });
}

module.exports = { testScheduler, testGuardrails };
