const db = require('./database');

// Insert test projects
const projectId1 = 'test-proj-1';
const projectId2 = 'test-proj-2';

console.log('Adding test data...');

db.run(`INSERT OR REPLACE INTO projects (id, name, description, status, ai_enabled, ai_processing_enabled, tech_stack) VALUES 
  (?, 'E-commerce Platform', 'Modern e-commerce solution', 'active', 1, 1, 'react'),
  (?, 'Task Management App', 'Productivity task manager', 'active', 1, 1, 'react')`, 
  [projectId1, projectId2], function(err) {
    if (err) {
      console.error('Error inserting projects:', err);
      return;
    }
    console.log('✅ Test projects created');

    // Insert test user stories
    db.run(`INSERT OR REPLACE INTO user_stories (id, project_id, title, description, priority, status, acceptance_criteria) VALUES
      ('story-1', ?, 'User Authentication', 'Implement user login and registration', 'high', 'in_progress', 'Users can register and login securely'),
      ('story-2', ?, 'Product Catalog', 'Create product listing and search', 'medium', 'in_progress', 'Products are displayed with search functionality'),
      ('story-3', ?, 'Task Creation', 'Allow users to create and edit tasks', 'high', 'in_progress', 'Users can create, edit and delete tasks')`,
      [projectId1, projectId1, projectId2], function(err) {
        if (err) {
          console.error('Error inserting stories:', err);
          return;
        }
        console.log('✅ Test user stories created');

        // Insert test AI processing queue items
        db.run(`INSERT OR REPLACE INTO ai_processing_queue (id, project_id, entity_type, entity_id, status, started_at, metadata) VALUES 
          ('queue-1', ?, 'user_story', 'story-1', 'processing', datetime('now', '-5 minutes'), '{}'),
          ('queue-2', ?, 'user_story', 'story-2', 'processing', datetime('now', '-2 minutes'), '{}'),
          ('queue-3', ?, 'user_story', 'story-3', 'processing', datetime('now', '-1 minute'), '{}')`, 
          [projectId1, projectId1, projectId2], function(err) {
            if (err) {
              console.error('Error inserting queue items:', err);
              return;
            }
            console.log('✅ Test AI processing queue items created');
            console.log('Test data setup complete!');
            process.exit(0);
        });
    });
});
