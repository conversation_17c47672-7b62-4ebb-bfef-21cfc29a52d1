import { useState, useEffect } from 'react';
import { 
  MessageCircle, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  User, 
  Bot,
  ChevronDown,
  ChevronUp,
  Filter,
  Eye,
  EyeOff
} from 'lucide-react';
import { format, formatDistanceToNow } from 'date-fns';
import './CommentTimeline.css';

const CommentTimeline = ({ 
  projectId, 
  entityType, 
  entityId, 
  showInternalComments = false,
  maxHeight = '400px'
}) => {
  const [comments, setComments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [expanded, setExpanded] = useState(true);
  const [filterType, setFilterType] = useState('all');
  const [showInternal, setShowInternal] = useState(showInternalComments);

  useEffect(() => {
    fetchComments();
  }, [projectId, entityType, entityId, showInternal]);

  const fetchComments = async () => {
    try {
      setLoading(true);
      const response = await fetch(
        `/api/projects/${projectId}/comments/${entityType}/${entityId}?includeInternal=${showInternal}`
      );
      
      if (!response.ok) {
        throw new Error('Failed to fetch comments');
      }
      
      const data = await response.json();
      setComments(data.comments || []);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const getCommentIcon = (commentType, authorType) => {
    if (authorType === 'ai_agent') {
      switch (commentType) {
        case 'blocker':
        case 'critical':
          return <AlertTriangle className="comment-icon blocker" size={16} />;
        case 'decision':
          return <CheckCircle className="comment-icon decision" size={16} />;
        case 'progress':
          return <Clock className="comment-icon progress" size={16} />;
        default:
          return <Bot className="comment-icon ai" size={16} />;
      }
    }
    return <User className="comment-icon human" size={16} />;
  };

  const getCommentTypeColor = (commentType, priority) => {
    if (priority === 'critical') return 'critical';
    if (priority === 'high') return 'high';
    
    switch (commentType) {
      case 'blocker': return 'blocker';
      case 'decision': return 'decision';
      case 'progress': return 'progress';
      case 'update': return 'update';
      default: return 'default';
    }
  };

  const filteredComments = comments.filter(comment => {
    if (filterType === 'all') return true;
    if (filterType === 'blockers') return comment.comment_type === 'blocker' || comment.priority === 'critical';
    if (filterType === 'decisions') return comment.comment_type === 'decision';
    if (filterType === 'progress') return comment.comment_type === 'progress';
    if (filterType === 'ai_only') return comment.author_type === 'ai_agent';
    if (filterType === 'human_only') return comment.author_type === 'human';
    return true;
  });

  const formatCommentText = (text) => {
    // Handle decision format
    if (text.includes('DECISION:') && text.includes('REASONING:')) {
      const parts = text.split('REASONING:');
      const decision = parts[0].replace('DECISION:', '').trim();
      const reasoning = parts[1]?.trim();
      
      return (
        <div className="decision-format">
          <div className="decision-title">
            <strong>Decision:</strong> {decision}
          </div>
          {reasoning && (
            <div className="decision-reasoning">
              <strong>Reasoning:</strong> {reasoning}
            </div>
          )}
        </div>
      );
    }
    
    // Handle regular text with line breaks
    return text.split('\n').map((line, index) => (
      <div key={index}>{line}</div>
    ));
  };

  if (loading) {
    return (
      <div className="comment-timeline loading">
        <div className="loading-spinner">Loading comments...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="comment-timeline error">
        <div className="error-message">
          <AlertTriangle size={16} />
          Error loading comments: {error}
        </div>
      </div>
    );
  }

  return (
    <div className="comment-timeline">
      <div className="timeline-header">
        <div className="timeline-title">
          <button 
            className="expand-toggle"
            onClick={() => setExpanded(!expanded)}
          >
            {expanded ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
            <MessageCircle size={16} />
            <span>AI Progress & Comments ({comments.length})</span>
          </button>
        </div>
        
        {expanded && (
          <div className="timeline-controls">
            <select 
              value={filterType} 
              onChange={(e) => setFilterType(e.target.value)}
              className="filter-select"
            >
              <option value="all">All Comments</option>
              <option value="blockers">Blockers & Critical</option>
              <option value="decisions">Decisions</option>
              <option value="progress">Progress Updates</option>
              <option value="ai_only">AI Only</option>
              <option value="human_only">Human Only</option>
            </select>
            
            <button
              className={`internal-toggle ${showInternal ? 'active' : ''}`}
              onClick={() => setShowInternal(!showInternal)}
              title={showInternal ? 'Hide internal AI comments' : 'Show internal AI comments'}
            >
              {showInternal ? <Eye size={14} /> : <EyeOff size={14} />}
              Internal
            </button>
          </div>
        )}
      </div>

      {expanded && (
        <div className="timeline-content" style={{ maxHeight }}>
          {filteredComments.length === 0 ? (
            <div className="no-comments">
              <MessageCircle size={24} />
              <p>No comments found</p>
            </div>
          ) : (
            <div className="timeline-items">
              {filteredComments.map((comment) => (
                <div 
                  key={comment.id} 
                  className={`timeline-item ${getCommentTypeColor(comment.comment_type, comment.priority)}`}
                >
                  <div className="timeline-marker">
                    {getCommentIcon(comment.comment_type, comment.author_type)}
                  </div>
                  
                  <div className="timeline-content-item">
                    <div className="comment-header">
                      <div className="comment-author">
                        <span className="author-name">{comment.author_name}</span>
                        <span className="comment-type">{comment.comment_type}</span>
                        {comment.priority !== 'normal' && (
                          <span className={`priority-badge ${comment.priority}`}>
                            {comment.priority}
                          </span>
                        )}
                      </div>
                      <div className="comment-time">
                        <span title={format(new Date(comment.created_at), 'PPpp')}>
                          {formatDistanceToNow(new Date(comment.created_at), { addSuffix: true })}
                        </span>
                      </div>
                    </div>
                    
                    <div className="comment-text">
                      {formatCommentText(comment.comment_text)}
                    </div>
                    
                    {comment.metadata && Object.keys(comment.metadata).length > 0 && (
                      <div className="comment-metadata">
                        <details>
                          <summary>Additional Details</summary>
                          <pre>{JSON.stringify(comment.metadata, null, 2)}</pre>
                        </details>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default CommentTimeline;
