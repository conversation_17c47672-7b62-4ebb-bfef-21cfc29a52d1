const db = require('./database');

async function migrateDatabase() {
  console.log('🔄 Starting database migration...');

  try {
    // Add missing columns to projects table
    console.log('Adding columns to projects table...');
    
    await runQuery(`ALTER TABLE projects ADD COLUMN ai_processing_enabled BOOLEAN DEFAULT 1`);
    console.log('✅ Added ai_processing_enabled to projects');
    
    await runQuery(`ALTER TABLE projects ADD COLUMN ai_last_activity TIMESTAMP`);
    console.log('✅ Added ai_last_activity to projects');
    
    await runQuery(`ALTER TABLE projects ADD COLUMN ai_processing_status TEXT DEFAULT 'idle' CHECK (ai_processing_status IN ('idle', 'processing', 'paused', 'error'))`);
    console.log('✅ Added ai_processing_status to projects');

    // Add missing columns to sprints table
    console.log('\nAdding columns to sprints table...');
    
    await runQuery(`ALTER TABLE sprints ADD COLUMN ai_processing_enabled BOOLEAN DEFAULT 1`);
    console.log('✅ Added ai_processing_enabled to sprints');
    
    await runQuery(`ALTER TABLE sprints ADD COLUMN ai_last_activity TIMESTAMP`);
    console.log('✅ Added ai_last_activity to sprints');
    
    await runQuery(`ALTER TABLE sprints ADD COLUMN stories_completed INTEGER DEFAULT 0`);
    console.log('✅ Added stories_completed to sprints');
    
    await runQuery(`ALTER TABLE sprints ADD COLUMN stories_total INTEGER DEFAULT 0`);
    console.log('✅ Added stories_total to sprints');

    console.log('\n🎉 Database migration completed successfully!');
    
  } catch (error) {
    if (error.message.includes('duplicate column name')) {
      console.log('⚠️ Columns already exist, skipping migration');
    } else {
      console.error('❌ Migration failed:', error);
      throw error;
    }
  }
}

function runQuery(sql) {
  return new Promise((resolve, reject) => {
    db.run(sql, function(err) {
      if (err) {
        reject(err);
      } else {
        resolve(this);
      }
    });
  });
}

if (require.main === module) {
  migrateDatabase()
    .then(() => {
      console.log('Migration script completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('Migration script failed:', error);
      process.exit(1);
    });
}

module.exports = { migrateDatabase };
