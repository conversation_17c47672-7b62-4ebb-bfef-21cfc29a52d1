/* Dashboard Sidebar Styles */
.dashboard-sidebar {
  width: 280px;
  height: 100vh;
  background: #1a1a1a;
  border-right: 1px solid #2d2d2d;
  display: flex;
  flex-direction: column;
  color: #e5e5e5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow-y: auto;
}

/* Header */
.sidebar-header {
  padding: 20px 16px;
  border-bottom: 1px solid #2d2d2d;
}

.brand-logo {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.logo-icon {
  width: 32px;
  height: 32px;
  background: #3b82f6;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-weight: 800;
  font-size: 16px;
}

.brand-logo h1 {
  margin: 0;
  color: #ffffff;
  font-size: 20px;
  font-weight: 700;
}

.brand-subtitle {
  margin: 0;
  color: #9ca3af;
  font-size: 13px;
  font-weight: 500;
}

/* Create Project Button */
.create-project-sidebar-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: calc(100% - 32px);
  margin: 16px 16px;
  padding: 10px 16px;
  background: #3b82f6;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
}

.create-project-sidebar-btn:hover {
  background: #2563eb;
}

/* Navigation */
.sidebar-nav {
  flex: 1;
  padding: 16px 0;
  overflow-y: auto;
}

.nav-section {
  margin-bottom: 24px;
}

.nav-section-header {
  padding: 0 16px 8px;
  font-size: 11px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 8px 16px;
  background: none;
  border: none;
  color: #d1d5db;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.15s ease;
  text-align: left;
}

.nav-item:hover {
  background: #2d2d2d;
  color: #ffffff;
}

.nav-item.active {
  background: #3b82f6;
  color: #ffffff;
}

.nav-item.active .nav-icon {
  color: #ffffff;
}

.nav-icon {
  color: #9ca3af;
  transition: color 0.15s ease;
}

.nav-item:hover .nav-icon {
  color: #ffffff;
}

.nav-count {
  background: #374151;
  color: #d1d5db;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  min-width: 20px;
  text-align: center;
  margin-left: auto;
}

.nav-item.active .nav-count {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

/* Status List */
.status-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 12px;
  margin-bottom: 2px;
  border-radius: 6px;
  transition: background 0.15s ease;
}

.status-item:hover {
  background: #2d2d2d;
}

.status-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-content {
  display: flex;
  flex-direction: row;
  align-items: baseline;
  justify-content: space-between;
  flex: 1;
}

.status-label {
  color: #d1d5db;
  font-size: 13px;
  font-weight: 500;
}

.status-count {
  color: #9ca3af;
  font-size: 12px;
  font-weight: 600;
  margin-left: 8px;
}

/* Responsive */
@media (max-width: 768px) {
  .dashboard-sidebar {
    width: 100%;
    height: auto;
    position: relative;
  }
  
  .sidebar-header {
    padding: 16px;
  }
  
  .create-project-sidebar-btn {
    margin: 12px 16px;
  }
  
  .sidebar-nav {
    padding: 0 16px 16px;
  }
  
  .nav-section {
    margin-bottom: 24px;
  }
}
