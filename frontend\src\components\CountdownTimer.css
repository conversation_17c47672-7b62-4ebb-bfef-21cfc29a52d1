.countdown-timer {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 24px 32px;
  margin-bottom: 24px;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.countdown-timer.stopped {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  box-shadow: 0 8px 32px rgba(108, 117, 125, 0.3);
}

.countdown-timer.expired {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  box-shadow: 0 8px 32px rgba(40, 167, 69, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 8px 32px rgba(40, 167, 69, 0.3);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 12px 40px rgba(40, 167, 69, 0.5);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 8px 32px rgba(40, 167, 69, 0.3);
  }
}

.timer-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  backdrop-filter: blur(10px);
}

.timer-content {
  flex: 1;
}

.timer-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.timer-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.tick-interval {
  padding: 4px 12px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.timer-display {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 12px;
}

.time-value {
  font-size: 2.5rem;
  font-weight: 700;
  font-family: 'Courier New', monospace;
  letter-spacing: 2px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.expired-label {
  padding: 6px 16px;
  background: rgba(255, 255, 255, 0.9);
  color: #28a745;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  animation: blink 1.5s infinite;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.5;
  }
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.progress-fill {
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 3px;
  transition: width 1s ease;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.countdown-timer.stopped .timer-content p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

/* Responsive design */
@media (max-width: 768px) {
  .countdown-timer {
    flex-direction: column;
    text-align: center;
    padding: 20px;
    gap: 16px;
  }
  
  .timer-header {
    flex-direction: column;
    gap: 8px;
  }
  
  .timer-header h2 {
    font-size: 1.25rem;
  }
  
  .time-value {
    font-size: 2rem;
  }
  
  .timer-display {
    flex-direction: column;
    gap: 12px;
  }
}
