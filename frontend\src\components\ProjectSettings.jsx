import React, { useState } from 'react';
import { Trash2, AlertTriangle } from 'lucide-react';
import './ProjectSettings.css';

const ProjectSettings = ({ project, onDeleteProject }) => {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [deleteConfirmText, setDeleteConfirmText] = useState('');
  const [deletionPreview, setDeletionPreview] = useState(null);
  const [loadingPreview, setLoadingPreview] = useState(false);

  const handleDeleteClick = async () => {
    setLoadingPreview(true);
    try {
      const response = await fetch(`/api/projects/${project.id}/deletion-preview`);
      if (response.ok) {
        const preview = await response.json();
        setDeletionPreview(preview);
      }
    } catch (error) {
      console.error('Error fetching deletion preview:', error);
    } finally {
      setLoadingPreview(false);
    }
    setShowDeleteConfirm(true);
  };

  const handleDeleteConfirm = () => {
    if (deleteConfirmText === project.name) {
      onDeleteProject();
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteConfirm(false);
    setDeleteConfirmText('');
    setDeletionPreview(null);
  };

  const isDeleteEnabled = deleteConfirmText === project.name;

  return (
    <div className="project-settings">
      <div className="settings-header">
        <h2>Project Settings</h2>
        <p>Manage your project configuration and danger zone actions.</p>
      </div>

      <div className="settings-content">
        {/* Project Information Section */}
        <div className="settings-section">
          <h3>Project Information</h3>
          <div className="project-info-grid">
            <div className="info-item">
              <label>Project Name</label>
              <span>{project.name}</span>
            </div>
            <div className="info-item">
              <label>Status</label>
              <span className={`status-badge status-${project.status}`}>
                {project.status}
              </span>
            </div>
            <div className="info-item">
              <label>Tech Stack</label>
              <span>{project.tech_stack}</span>
            </div>
            <div className="info-item">
              <label>Created</label>
              <span>{new Date(project.created_at).toLocaleDateString()}</span>
            </div>
          </div>
        </div>

        {/* AI Configuration Section */}
        <div className="settings-section">
          <h3>AI Configuration</h3>
          <div className="ai-config-grid">
            <div className="config-item">
              <label>AI Enabled</label>
              <span className={`status-indicator ${project.ai_enabled ? 'enabled' : 'disabled'}`}>
                {project.ai_enabled ? 'Enabled' : 'Disabled'}
              </span>
            </div>
            <div className="config-item">
              <label>AI Processing</label>
              <span className={`status-indicator ${project.ai_processing_enabled ? 'enabled' : 'disabled'}`}>
                {project.ai_processing_enabled ? 'Enabled' : 'Disabled'}
              </span>
            </div>
          </div>
        </div>

        {/* Danger Zone Section */}
        <div className="settings-section danger-zone">
          <h3>
            <AlertTriangle size={20} />
            Danger Zone
          </h3>
          <p>
            Once you delete a project, there is no going back. This will permanently delete:
          </p>
          <ul>
            <li>All user stories and tasks</li>
            <li>All sprints and sprint reviews</li>
            <li>All comments and AI processing history</li>
            <li>Project workspace and files</li>
          </ul>

          {!showDeleteConfirm ? (
            <button
              className="btn-danger delete-project-btn"
              onClick={handleDeleteClick}
              disabled={loadingPreview}
            >
              <Trash2 size={16} />
              {loadingPreview ? 'Loading...' : 'Delete Project'}
            </button>
          ) : (
            <div className="delete-confirmation">
              <div className="confirmation-content">
                <h4>Are you absolutely sure?</h4>
                <p>
                  This action <strong>cannot be undone</strong>. This will permanently delete the{' '}
                  <strong>{project.name}</strong> project and all of its data.
                </p>

                {deletionPreview && (
                  <div className="deletion-preview">
                    <h5>What will be deleted:</h5>
                    <ul>
                      <li><strong>{deletionPreview.will_delete.database.user_stories}</strong> user stories</li>
                      <li><strong>{deletionPreview.will_delete.database.sprints}</strong> sprints</li>
                      <li><strong>{deletionPreview.will_delete.database.comments}</strong> comments</li>
                      <li><strong>{deletionPreview.will_delete.database.ai_queue_items}</strong> AI processing queue items</li>
                      <li>Workspace directory: <strong>{deletionPreview.will_delete.workspace.exists ? 'EXISTS' : 'NOT FOUND'}</strong></li>
                    </ul>
                  </div>
                )}

                <p>
                  Please type <strong>{project.name}</strong> to confirm:
                </p>
                <input
                  type="text"
                  value={deleteConfirmText}
                  onChange={(e) => setDeleteConfirmText(e.target.value)}
                  placeholder="Type project name here"
                  className="delete-confirm-input"
                  autoFocus
                />
                <div className="confirmation-buttons">
                  <button
                    className="btn-danger"
                    onClick={handleDeleteConfirm}
                    disabled={!isDeleteEnabled}
                  >
                    <Trash2 size={16} />
                    I understand the consequences, delete this project
                  </button>
                  <button
                    className="btn-secondary"
                    onClick={handleDeleteCancel}
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProjectSettings;
