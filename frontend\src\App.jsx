import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { useState } from 'react';
import ProjectDashboard from './components/ProjectDashboard';
import ProjectDetail from './components/ProjectDetail';
import ProjectModal from './components/ProjectModal';
import Reports from './components/Reports';
import './App.css';

function App() {
  const [showCreateModal, setShowCreateModal] = useState(false);

  const handleProjectCreate = () => {
    // Refresh the dashboard or handle project creation
    setShowCreateModal(false);
    window.location.reload(); // Simple refresh for now
  };

  return (
    <Router>
      <div className="App">
        <Routes>
          <Route path="/" element={<ProjectDashboard />} />
          <Route path="/projects/:id" element={<ProjectDetail />} />
          <Route path="/projects/:id/reports" element={<Reports />} />
          <Route path="/reports" element={<Reports isDashboard={true} />} />
        </Routes>

        <ProjectModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onSave={handleProjectCreate}
        />
      </div>
    </Router>
  );
}

export default App;
