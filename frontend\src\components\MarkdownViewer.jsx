import { useState, useEffect } from 'react';
import './MarkdownViewer.css';

const MarkdownViewer = ({ content, className = '' }) => {
  const [htmlContent, setHtmlContent] = useState('');

  useEffect(() => {
    if (content) {
      const html = convertMarkdownToHtml(content);
      setHtmlContent(html);
    }
  }, [content]);

  // Enhanced markdown to HTML converter
  const convertMarkdownToHtml = (markdown) => {
    let html = markdown;
    
    // Escape HTML entities first
    html = html.replace(/&/g, '&amp;')
               .replace(/</g, '&lt;')
               .replace(/>/g, '&gt;');
    
    // Headers (must be done in order from largest to smallest)
    html = html.replace(/^#### (.*$)/gim, '<h4>$1</h4>');
    html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>');
    html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>');
    html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>');
    
    // Code blocks (must be done before inline code)
    html = html.replace(/```(\w+)?\n([\s\S]*?)```/gim, (match, lang, code) => {
      const language = lang || 'text';
      return `<pre class="code-block"><code class="language-${language}">${code.trim()}</code></pre>`;
    });
    
    // Inline code
    html = html.replace(/`([^`\n]+)`/gim, '<code class="inline-code">$1</code>');
    
    // Bold and italic (must be done in correct order)
    html = html.replace(/\*\*\*(.*?)\*\*\*/gim, '<strong><em>$1</em></strong>');
    html = html.replace(/\*\*(.*?)\*\*/gim, '<strong>$1</strong>');
    html = html.replace(/\*(.*?)\*/gim, '<em>$1</em>');
    
    // Links
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/gim, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>');
    
    // Images
    html = html.replace(/!\[([^\]]*)\]\(([^)]+)\)/gim, '<img src="$2" alt="$1" />');
    
    // Horizontal rules
    html = html.replace(/^---$/gim, '<hr>');
    html = html.replace(/^\*\*\*$/gim, '<hr>');
    
    // Blockquotes
    html = html.replace(/^&gt; (.*$)/gim, '<blockquote>$1</blockquote>');
    
    // Lists - handle nested lists
    const lines = html.split('\n');
    const processedLines = [];
    let inList = false;
    let listType = null;
    let listLevel = 0;
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmedLine = line.trim();
      
      // Check for list items
      const unorderedMatch = line.match(/^(\s*)[-*+] (.*)$/);
      const orderedMatch = line.match(/^(\s*)\d+\. (.*)$/);
      
      if (unorderedMatch || orderedMatch) {
        const indent = (unorderedMatch ? unorderedMatch[1] : orderedMatch[1]).length;
        const content = unorderedMatch ? unorderedMatch[2] : orderedMatch[2];
        const currentListType = unorderedMatch ? 'ul' : 'ol';
        
        if (!inList) {
          processedLines.push(`<${currentListType}>`);
          inList = true;
          listType = currentListType;
          listLevel = indent;
        } else if (listType !== currentListType || indent !== listLevel) {
          // Handle nested lists or list type changes
          processedLines.push(`</${listType}>`);
          processedLines.push(`<${currentListType}>`);
          listType = currentListType;
          listLevel = indent;
        }
        
        processedLines.push(`<li>${content}</li>`);
      } else {
        if (inList && trimmedLine === '') {
          // Empty line in list - continue list
          processedLines.push(line);
        } else if (inList) {
          // End of list
          processedLines.push(`</${listType}>`);
          inList = false;
          listType = null;
          listLevel = 0;
          processedLines.push(line);
        } else {
          processedLines.push(line);
        }
      }
    }
    
    // Close any remaining open list
    if (inList) {
      processedLines.push(`</${listType}>`);
    }
    
    html = processedLines.join('\n');
    
    // Tables
    html = html.replace(/\|(.+)\|/g, (match, content) => {
      const cells = content.split('|').map(cell => cell.trim());
      const cellTags = cells.map(cell => `<td>${cell}</td>`).join('');
      return `<tr>${cellTags}</tr>`;
    });
    
    // Wrap table rows in table tags
    html = html.replace(/(<tr>.*<\/tr>\s*)+/gims, (match) => {
      const rows = match.trim().split('\n');
      const headerRow = rows[0];
      const bodyRows = rows.slice(1);
      
      let table = '<table class="markdown-table">';
      if (headerRow) {
        const headerCells = headerRow.replace(/<td>/g, '<th>').replace(/<\/td>/g, '</th>');
        table += `<thead>${headerCells}</thead>`;
      }
      if (bodyRows.length > 0) {
        table += `<tbody>${bodyRows.join('')}</tbody>`;
      }
      table += '</table>';
      
      return table;
    });
    
    // Paragraphs - split by double newlines and wrap in <p> tags
    const paragraphs = html.split(/\n\s*\n/);
    html = paragraphs.map(para => {
      const trimmed = para.trim();
      if (!trimmed) return '';
      
      // Don't wrap if it's already a block element
      if (trimmed.match(/^<(h[1-6]|div|p|ul|ol|li|blockquote|pre|table|hr)/)) {
        return trimmed;
      }
      
      return `<p>${trimmed}</p>`;
    }).join('\n\n');
    
    // Clean up extra line breaks
    html = html.replace(/\n/g, ' ');
    html = html.replace(/\s+/g, ' ');
    html = html.replace(/>\s+</g, '><');
    
    return html;
  };

  if (!content) {
    return (
      <div className={`markdown-viewer ${className}`}>
        <div className="empty-content">
          <p>No content to display</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`markdown-viewer ${className}`}>
      <div 
        className="markdown-content"
        dangerouslySetInnerHTML={{ __html: htmlContent }}
      />
    </div>
  );
};

export default MarkdownViewer;
