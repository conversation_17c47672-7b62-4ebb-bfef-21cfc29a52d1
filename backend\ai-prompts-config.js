const { nanoid } = require('nanoid');

// Default AI agent prompt templates
const DEFAULT_PROMPTS = {
  product_owner: {
    agent_type: 'product_owner',
    description: 'Product Owner AI - Refines user stories, adds edge cases, dependencies, and definition of done',
    prompt_template: `You are a Product Owner AI. Refine this user story and respond ONLY with valid JSON.

Story Details:
- Title: \${userStory.title}
- Description: \${userStory.description}
- Acceptance Criteria: \${userStory.acceptance_criteria}
- Priority: \${userStory.priority}
- Story Points: \${userStory.story_points}

Previous Comments and Context:
\${commentsSummary}

\${blockers.length > 0 ? \`IMPORTANT - Active Blockers Found:
\${blockers.map(b => \`- \${b.comment_text}\`).join('\\n')}

Please address these blockers in your refinement.\` : ''}

Return ONLY this JSON structure (no other text):
{
  "refined_acceptance_criteria": "More specific and testable criteria",
  "edge_cases": ["Edge case 1", "Edge case 2"],
  "dependencies": ["Dependency 1", "Dependency 2"],
  "definition_of_done": ["Done criteria 1", "Done criteria 2"]
}`
  },

  scrum_master: {
    agent_type: 'scrum_master',
    description: 'Scrum Master AI - Breaks down user stories into development tasks',
    prompt_template: `You are a Scrum Master AI. Break down this user story into development tasks and respond ONLY with valid JSON.

Story Details:
- Title: \${userStory.title}
- Description: \${userStory.description}
- Acceptance Criteria: \${userStory.acceptance_criteria}
- Refined Acceptance Criteria: \${userStory.refined_acceptance_criteria || 'Not refined yet'}
- Edge Cases: \${JSON.stringify(userStory.edge_cases || [])}
- Dependencies: \${JSON.stringify(userStory.dependencies || [])}
- Definition of Done: \${JSON.stringify(userStory.definition_of_done || [])}
- Tech Stack: React + Node.js

Previous Comments and Context:
\${commentsSummary}

\${blockers.length > 0 ? \`IMPORTANT - Active Blockers:
\${blockers.map(b => \`- \${b.comment_text}\`).join('\\n')}

Consider these blockers when creating tasks.\` : ''}

Return ONLY this JSON structure (no other text):
{
  "tasks": [
    {
      "title": "Task title",
      "description": "Task description",
      "estimated_hours": 4,
      "priority": "high",
      "type": "frontend"
    }
  ]
}`
  },

  lead_developer: {
    agent_type: 'lead_developer',
    description: 'Lead Developer AI - Generates actual working code for user stories',
    prompt_template: `You are a Lead Developer AI. Generate ACTUAL WORKING CODE for this user story and respond ONLY with valid JSON.

Story Details:
- Title: \${userStory.title}
- Description: \${userStory.description}
- Refined Acceptance Criteria: \${userStory.refined_acceptance_criteria || 'Not refined yet'}
- Edge Cases: \${JSON.stringify(userStory.edge_cases || [])}
- Dependencies: \${JSON.stringify(userStory.dependencies || [])}
- Definition of Done: \${JSON.stringify(userStory.definition_of_done || [])}
- Tech Stack: \${techStackInfo.name}

IMPORTANT TECH STACK REQUIREMENTS:
\${techStackInfo.requirements}

Previous Comments and Context:
\${commentsSummary}

CRITICAL: Generate COMPLETE, FUNCTIONAL CODE - not comments or placeholders!

\${techStackInfo.examples}

Return ONLY this JSON structure (no other text):
{
  "files": [
    {
      "path": "relative/path/to/file.ext",
      "content": "COMPLETE WORKING CODE HERE - NO COMMENTS OR PLACEHOLDERS",
      "description": "Brief description of what this file does"
    }
  ]
}`
  }
};

/**
 * Get default prompt for an agent type
 * @param {string} agentType - The agent type (product_owner, scrum_master, lead_developer)
 * @returns {Object} Default prompt configuration
 */
function getDefaultPrompt(agentType) {
  return DEFAULT_PROMPTS[agentType] || null;
}

/**
 * Get all default prompts
 * @returns {Object} All default prompt configurations
 */
function getAllDefaultPrompts() {
  return DEFAULT_PROMPTS;
}

/**
 * Initialize default prompts for a project
 * @param {Object} db - Database connection
 * @param {string} projectId - Project ID
 * @returns {Promise} Promise that resolves when prompts are initialized
 */
async function initializeDefaultPrompts(db, projectId) {
  return new Promise((resolve, reject) => {
    const prompts = Object.values(DEFAULT_PROMPTS);
    let completed = 0;
    let hasError = false;

    if (prompts.length === 0) {
      resolve();
      return;
    }

    prompts.forEach(prompt => {
      const id = nanoid();
      const query = `INSERT OR REPLACE INTO ai_prompts (
        id, project_id, agent_type, prompt_template, description, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`;

      db.run(query, [
        id,
        projectId,
        prompt.agent_type,
        prompt.prompt_template,
        prompt.description
      ], (err) => {
        if (err && !hasError) {
          hasError = true;
          reject(err);
          return;
        }

        completed++;
        if (completed === prompts.length && !hasError) {
          resolve();
        }
      });
    });
  });
}

module.exports = {
  DEFAULT_PROMPTS,
  getDefaultPrompt,
  getAllDefaultPrompts,
  initializeDefaultPrompts
};
