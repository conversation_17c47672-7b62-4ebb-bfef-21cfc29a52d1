/* Team View Styles */
.team-view {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  color: #e5e5e5;
}

.team-header {
  margin-bottom: 2rem;
}

.team-header h1 {
  margin: 0 0 0.5rem 0;
  color: #ffffff;
  font-size: 2rem;
  font-weight: 700;
}

.team-header p {
  margin: 0;
  color: #9ca3af;
  font-size: 1.1rem;
  font-weight: 500;
}

.team-view-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
  color: #9ca3af;
}

.team-view-loading .loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #2d2d2d;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Agents Grid */
.agents-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 2rem;
}

/* Agent Card */
.agent-card {
  background: #1a1a1a;
  border-radius: 12px;
  border: 1px solid #2d2d2d;
  padding: 1.5rem;
  transition: border-color 0.15s ease, box-shadow 0.15s ease;
}

.agent-card:hover {
  border-color: #374151;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Agent Header */
.agent-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.agent-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.agent-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.agent-details h3 {
  margin: 0;
  color: #ffffff;
  font-size: 1.25rem;
  font-weight: 600;
}

.agent-role {
  color: #9ca3af;
  font-size: 0.875rem;
  font-weight: 500;
}

.agent-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-icon {
  background: #2d2d2d;
  border: 1px solid #374151;
  border-radius: 6px;
  padding: 0.5rem;
  color: #9ca3af;
  cursor: pointer;
  transition: all 0.15s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-icon:hover {
  background: #374151;
  color: #ffffff;
  border-color: #4b5563;
}

.btn-icon:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-save {
  background: #10b981;
  border-color: #10b981;
  color: white;
}

.btn-save:hover:not(:disabled) {
  background: #059669;
  border-color: #059669;
}

.btn-cancel {
  background: #ef4444;
  border-color: #ef4444;
  color: white;
}

.btn-cancel:hover {
  background: #dc2626;
  border-color: #dc2626;
}

.btn-reset {
  background: #f59e0b;
  border-color: #f59e0b;
  color: white;
}

.btn-reset:hover {
  background: #d97706;
  border-color: #d97706;
}

/* Agent Description */
.agent-description {
  margin-bottom: 1.5rem;
}

.agent-description p {
  margin: 0;
  color: #9ca3af;
  line-height: 1.5;
}

.description-input {
  width: 100%;
  background: #2d2d2d;
  border: 1px solid #374151;
  border-radius: 6px;
  padding: 0.75rem;
  color: #ffffff;
  font-size: 0.875rem;
  transition: border-color 0.15s ease;
}

.description-input:focus {
  outline: none;
  border-color: #3b82f6;
}

/* Prompt Section */
.prompt-section {
  margin-bottom: 1rem;
}

.prompt-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.prompt-header h4 {
  margin: 0;
  color: #ffffff;
  font-size: 1rem;
  font-weight: 600;
}

.prompt-info {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #6b7280;
  font-size: 0.75rem;
}

.prompt-editor {
  width: 100%;
  background: #2d2d2d;
  border: 1px solid #374151;
  border-radius: 6px;
  padding: 1rem;
  color: #ffffff;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  resize: vertical;
  min-height: 300px;
  transition: border-color 0.15s ease;
}

.prompt-editor:focus {
  outline: none;
  border-color: #3b82f6;
}

.prompt-display {
  background: #0f0f0f;
  border: 1px solid #2d2d2d;
  border-radius: 6px;
  padding: 1rem;
  color: #e5e5e5;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 400px;
  overflow-y: auto;
  margin: 0;
}

/* Agent Footer */
.agent-footer {
  padding-top: 1rem;
  border-top: 1px solid #2d2d2d;
}

.last-updated {
  color: #6b7280;
  font-size: 0.75rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .team-view {
    padding: 1rem;
  }

  .agents-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .agent-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .agent-actions {
    align-self: flex-end;
  }

  .prompt-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .prompt-editor {
    min-height: 250px;
  }
}
