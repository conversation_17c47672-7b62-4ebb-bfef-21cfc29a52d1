import React, { useState, useEffect } from 'react';
import { Bot, Edit3, Save, X, RotateCcw, User, AlertCircle } from 'lucide-react';
import ConfirmationModal from './ConfirmationModal';
import './TeamView.css';

const TeamView = ({ projectId }) => {
  const [prompts, setPrompts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [editingAgent, setEditingAgent] = useState(null);
  const [editedPrompt, setEditedPrompt] = useState('');
  const [editedDescription, setEditedDescription] = useState('');
  const [saving, setSaving] = useState(false);
  const [showResetModal, setShowResetModal] = useState(false);
  const [resetAgent, setResetAgent] = useState(null);

  const agentInfo = {
    product_owner: {
      name: 'Product Owner AI',
      role: 'Story Refinement',
      color: '#10b981',
      icon: User,
      description: 'Refines user stories, adds edge cases, dependencies, and definition of done'
    },
    scrum_master: {
      name: 'Scrum Master AI',
      role: 'Task Management',
      color: '#3b82f6',
      icon: Bot,
      description: 'Breaks down user stories into development tasks and manages sprint planning'
    },
    lead_developer: {
      name: 'Lead Developer AI',
      role: 'Code Generation',
      color: '#8b5cf6',
      icon: Bot,
      description: 'Generates actual working code and technical architecture for user stories'
    }
  };

  useEffect(() => {
    fetchPrompts();
  }, [projectId]);

  const fetchPrompts = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/projects/${projectId}/ai/prompts`);
      if (response.ok) {
        const data = await response.json();
        setPrompts(data);
      } else {
        console.error('Failed to fetch prompts');
      }
    } catch (error) {
      console.error('Error fetching prompts:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (agent) => {
    const prompt = prompts.find(p => p.agent_type === agent.agent_type);
    setEditingAgent(agent.agent_type);
    setEditedPrompt(prompt?.prompt_template || '');
    setEditedDescription(prompt?.description || '');
  };

  const handleSave = async () => {
    if (!editingAgent) return;

    try {
      setSaving(true);
      const response = await fetch(`/api/projects/${projectId}/ai/prompts/${editingAgent}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt_template: editedPrompt,
          description: editedDescription
        })
      });

      if (response.ok) {
        await fetchPrompts();
        setEditingAgent(null);
        setEditedPrompt('');
        setEditedDescription('');
      } else {
        console.error('Failed to save prompt');
      }
    } catch (error) {
      console.error('Error saving prompt:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    setEditingAgent(null);
    setEditedPrompt('');
    setEditedDescription('');
  };

  const handleReset = (agentType) => {
    setResetAgent(agentType);
    setShowResetModal(true);
  };

  const confirmReset = async () => {
    if (!resetAgent) return;

    try {
      setSaving(true);
      const response = await fetch(`/api/projects/${projectId}/ai/prompts/${resetAgent}/reset`, {
        method: 'POST'
      });

      if (response.ok) {
        await fetchPrompts();
        setShowResetModal(false);
        setResetAgent(null);
      } else {
        console.error('Failed to reset prompt');
      }
    } catch (error) {
      console.error('Error resetting prompt:', error);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="team-view-loading">
        <div className="loading-spinner"></div>
        <p>Loading AI team configuration...</p>
      </div>
    );
  }

  return (
    <div className="team-view">
      <div className="team-header">
        <h1>AI Engineering Team</h1>
        <p>Configure and customize your AI agents' behavior and prompts</p>
      </div>

      <div className="agents-grid">
        {Object.entries(agentInfo).map(([agentType, info]) => {
          const prompt = prompts.find(p => p.agent_type === agentType);
          const isEditing = editingAgent === agentType;
          const IconComponent = info.icon;

          return (
            <div key={agentType} className="agent-card">
              <div className="agent-header">
                <div className="agent-info">
                  <div 
                    className="agent-icon"
                    style={{ backgroundColor: info.color }}
                  >
                    <IconComponent size={24} />
                  </div>
                  <div className="agent-details">
                    <h3>{info.name}</h3>
                    <span className="agent-role">{info.role}</span>
                  </div>
                </div>
                <div className="agent-actions">
                  {!isEditing ? (
                    <>
                      <button
                        className="btn-icon"
                        onClick={() => handleEdit({ agent_type: agentType })}
                        title="Edit prompt"
                      >
                        <Edit3 size={16} />
                      </button>
                      <button
                        className="btn-icon btn-reset"
                        onClick={() => handleReset(agentType)}
                        title="Reset to default"
                      >
                        <RotateCcw size={16} />
                      </button>
                    </>
                  ) : (
                    <>
                      <button
                        className="btn-icon btn-save"
                        onClick={handleSave}
                        disabled={saving}
                        title="Save changes"
                      >
                        <Save size={16} />
                      </button>
                      <button
                        className="btn-icon btn-cancel"
                        onClick={handleCancel}
                        title="Cancel editing"
                      >
                        <X size={16} />
                      </button>
                    </>
                  )}
                </div>
              </div>

              <div className="agent-description">
                {isEditing ? (
                  <input
                    type="text"
                    value={editedDescription}
                    onChange={(e) => setEditedDescription(e.target.value)}
                    placeholder="Agent description"
                    className="description-input"
                  />
                ) : (
                  <p>{prompt?.description || info.description}</p>
                )}
              </div>

              <div className="prompt-section">
                <div className="prompt-header">
                  <h4>Prompt Template</h4>
                  {!isEditing && (
                    <span className="prompt-info">
                      <AlertCircle size={14} />
                      Use ${'{variable}'} for dynamic content
                    </span>
                  )}
                </div>
                
                {isEditing ? (
                  <textarea
                    value={editedPrompt}
                    onChange={(e) => setEditedPrompt(e.target.value)}
                    className="prompt-editor"
                    rows={12}
                    placeholder="Enter the AI prompt template..."
                  />
                ) : (
                  <pre className="prompt-display">
                    {prompt?.prompt_template || 'No prompt configured'}
                  </pre>
                )}
              </div>

              {prompt?.updated_at && (
                <div className="agent-footer">
                  <span className="last-updated">
                    Last updated: {new Date(prompt.updated_at).toLocaleDateString()}
                  </span>
                </div>
              )}
            </div>
          );
        })}
      </div>

      <ConfirmationModal
        isOpen={showResetModal}
        onClose={() => setShowResetModal(false)}
        onConfirm={confirmReset}
        title="Reset AI Prompt"
        message={`Are you sure you want to reset the ${agentInfo[resetAgent]?.name} prompt to its default configuration? This action cannot be undone.`}
        confirmText="Reset to Default"
        cancelText="Cancel"
        isDestructive={true}
      />
    </div>
  );
};

export default TeamView;
