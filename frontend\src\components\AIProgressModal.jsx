import React, { useState, useEffect } from 'react';
import { X, Bot, MessageSquare, AlertTriangle, CheckCircle, Clock, Lightbulb } from 'lucide-react';
import './AIProgressModal.css';

const AIProgressModal = ({ isOpen, onClose, projectId, storyId, storyTitle }) => {
  const [comments, setComments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    type: 'all',
    priority: 'all',
    showInternal: true
  });

  useEffect(() => {
    if (isOpen && projectId && storyId) {
      fetchComments();
    }
  }, [isOpen, projectId, storyId]);

  const fetchComments = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/projects/${projectId}/comments/user_story/${storyId}`);
      if (!response.ok) throw new Error('Failed to fetch comments');
      
      const data = await response.json();
      setComments(data.comments || []);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const getFilteredComments = () => {
    return comments.filter(comment => {
      if (filters.type !== 'all' && comment.comment_type !== filters.type) return false;
      if (filters.priority !== 'all' && comment.priority !== filters.priority) return false;
      if (!filters.showInternal && comment.is_internal) return false;
      return true;
    });
  };

  const getCommentIcon = (type) => {
    switch (type) {
      case 'blocker': return <AlertTriangle size={16} />;
      case 'decision': return <CheckCircle size={16} />;
      case 'progress': return <Clock size={16} />;
      case 'update': return <Lightbulb size={16} />;
      default: return <MessageSquare size={16} />;
    }
  };

  const formatCommentText = (comment) => {
    if (comment.comment_type === 'decision' && comment.metadata) {
      try {
        const metadata = typeof comment.metadata === 'string' 
          ? JSON.parse(comment.metadata) 
          : comment.metadata;
        
        if (metadata.decision && metadata.reasoning) {
          return (
            <div className="decision-format">
              <div className="decision-title">
                <strong>Decision:</strong> {metadata.decision}
              </div>
              <div className="decision-reasoning">
                <strong>Reasoning:</strong> {metadata.reasoning}
              </div>
            </div>
          );
        }
      } catch (e) {
        // Fall back to regular text if parsing fails
      }
    }
    
    return <div className="comment-text">{comment.comment_text}</div>;
  };

  const getCommentStats = () => {
    const total = comments.length;
    const byType = comments.reduce((acc, comment) => {
      acc[comment.comment_type] = (acc[comment.comment_type] || 0) + 1;
      return acc;
    }, {});
    
    const blockers = comments.filter(c => c.comment_type === 'blocker' && c.priority === 'critical').length;
    
    return { total, byType, blockers };
  };

  if (!isOpen) return null;

  const filteredComments = getFilteredComments();
  const stats = getCommentStats();

  return (
    <div className="ai-progress-modal-overlay" onClick={onClose}>
      <div className="ai-progress-modal" onClick={e => e.stopPropagation()}>
        {/* Modal Header */}
        <div className="modal-header">
          <div className="modal-title">
            <Bot size={20} />
            <div>
              <h2>AI Progress & Comments</h2>
              <p className="story-title">{storyTitle}</p>
            </div>
          </div>
          <button className="close-button" onClick={onClose}>
            <X size={20} />
          </button>
        </div>

        {/* Stats Bar */}
        <div className="stats-bar">
          <div className="stat-item">
            <span className="stat-label">Total Comments:</span>
            <span className="stat-value">{stats.total}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Decisions:</span>
            <span className="stat-value">{stats.byType.decision || 0}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Progress Updates:</span>
            <span className="stat-value">{stats.byType.progress || 0}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Active Blockers:</span>
            <span className={`stat-value ${stats.blockers > 0 ? 'warning' : ''}`}>
              {stats.blockers}
            </span>
          </div>
        </div>

        {/* Filters */}
        <div className="filters-section">
          <div className="filter-group">
            <label>Type:</label>
            <select 
              value={filters.type} 
              onChange={(e) => setFilters({...filters, type: e.target.value})}
            >
              <option value="all">All Types</option>
              <option value="progress">Progress</option>
              <option value="decision">Decisions</option>
              <option value="blocker">Blockers</option>
              <option value="update">Updates</option>
            </select>
          </div>
          
          <div className="filter-group">
            <label>Priority:</label>
            <select 
              value={filters.priority} 
              onChange={(e) => setFilters({...filters, priority: e.target.value})}
            >
              <option value="all">All Priorities</option>
              <option value="normal">Normal</option>
              <option value="high">High</option>
              <option value="critical">Critical</option>
            </select>
          </div>
          
          <div className="filter-group">
            <label className="checkbox-label">
              <input
                type="checkbox"
                checked={filters.showInternal}
                onChange={(e) => setFilters({...filters, showInternal: e.target.checked})}
              />
              Show Internal Comments
            </label>
          </div>
        </div>

        {/* Content */}
        <div className="modal-content">
          {loading && (
            <div className="loading-state">
              <div className="spinner"></div>
              <p>Loading AI progress...</p>
            </div>
          )}

          {error && (
            <div className="error-state">
              <AlertTriangle size={24} />
              <p>Error loading comments: {error}</p>
              <button onClick={fetchComments} className="retry-button">Retry</button>
            </div>
          )}

          {!loading && !error && filteredComments.length === 0 && (
            <div className="empty-state">
              <MessageSquare size={48} />
              <h3>No comments found</h3>
              <p>No AI progress comments match your current filters.</p>
            </div>
          )}

          {!loading && !error && filteredComments.length > 0 && (
            <div className="comments-timeline">
              {filteredComments.map((comment, index) => (
                <div key={comment.id} className={`timeline-item ${comment.comment_type} ${comment.priority}`}>
                  <div className="timeline-marker">
                    {getCommentIcon(comment.comment_type)}
                  </div>
                  
                  <div className="timeline-content">
                    <div className="comment-header">
                      <div className="comment-meta">
                        <span className="author-name">{comment.author_name}</span>
                        <span className={`comment-type-badge ${comment.comment_type}`}>
                          {comment.comment_type}
                        </span>
                        {comment.priority !== 'normal' && (
                          <span className={`priority-badge ${comment.priority}`}>
                            {comment.priority}
                          </span>
                        )}
                        {comment.is_internal && (
                          <span className="internal-badge">Internal</span>
                        )}
                      </div>
                      <span className="comment-time">
                        {new Date(comment.created_at).toLocaleString()}
                      </span>
                    </div>
                    
                    <div className="comment-body">
                      {formatCommentText(comment)}
                    </div>
                    
                    {comment.metadata && comment.metadata !== '{}' && (
                      <details className="comment-metadata">
                        <summary>View Metadata</summary>
                        <pre>{JSON.stringify(
                          typeof comment.metadata === 'string' 
                            ? JSON.parse(comment.metadata) 
                            : comment.metadata, 
                          null, 2
                        )}</pre>
                      </details>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="modal-footer">
          <button onClick={onClose} className="btn-secondary">Close</button>
          <button onClick={fetchComments} className="btn-primary">Refresh</button>
        </div>
      </div>
    </div>
  );
};

export default AIProgressModal;
