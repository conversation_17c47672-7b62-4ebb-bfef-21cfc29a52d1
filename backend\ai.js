const axios = require('axios');

// WARNING: This is a placeholder key. Replace with your actual OpenRouter API key.
const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY || "YOUR_OPENROUTER_API_KEY";
const YOUR_SITE_URL = 'http://localhost:3000'; // or your actual site url
const YOUR_SITE_NAME = 'Quarrel - Project Management Platform';

const callOpenRouter = async (messages, model) => {
    if (OPENROUTER_API_KEY === "YOUR_OPENROUTER_API_KEY") {
        console.error("OpenRouter API key is not set. Please set the OPENROUTER_API_KEY environment variable or replace the placeholder in backend/ai.js");
        throw new Error("OpenRouter API key not set");
    }

    try {
        const response = await axios.post('https://openrouter.ai/api/v1/chat/completions', {
            model: model,
            messages: messages,
        }, {
            headers: {
                'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
                'HTTP-Referer': YOUR_SITE_URL,
                'X-Title': YOUR_SITE_NAME,
                'Content-Type': 'application/json'
            }
        });
        return response.data.choices[0].message.content;
    } catch (error) {
        console.error('Error calling OpenRouter API:', error);
        throw error;
    }
};

const getLeadDeveloperCompletion = async (prompt) => {
    const messages = [{ role: 'user', content: prompt }];
    // Using a powerful model for the lead developer
    return callOpenRouter(messages, 'anthropic/claude-3-opus-20240229');
};

const getProfessionalWorkerCompletion = async (prompt) => {
    const messages = [{ role: 'user', content: prompt }];
    // Using a cost-effective model for the professional workers
    return callOpenRouter(messages, 'anthropic/claude-3-haiku-20240307');
};

module.exports = {
    getLeadDeveloperCompletion,
    getProfessionalWorkerCompletion,
};
