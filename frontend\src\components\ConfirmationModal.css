/* Modal Backdrop */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

/* Modal Container */
.confirmation-modal {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  max-width: 480px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  animation: modalSlideIn 0.2s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Modal Header */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 24px 16px 24px;
  border-bottom: 1px solid var(--border-color);
}

.modal-title-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.modal-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.modal-icon {
  flex-shrink: 0;
}

.modal-icon.danger {
  color: var(--danger-color, #ef4444);
}

.modal-icon.warning {
  color: var(--warning-color, #f59e0b);
}

.modal-close-button {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.modal-close-button:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

/* Modal Body */
.modal-body {
  padding: 16px 24px 24px 24px;
}

.modal-message {
  margin: 0;
  color: var(--text-secondary);
  line-height: 1.5;
  font-size: 0.95rem;
}

/* Modal Footer */
.modal-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 0 24px 24px 24px;
}

.modal-footer .btn-secondary,
.modal-footer .btn-primary {
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  min-width: 80px;
}

.modal-footer .btn-secondary {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.modal-footer .btn-secondary:hover {
  background: var(--bg-quaternary);
  border-color: var(--border-hover);
}

.modal-footer .btn-primary {
  background: var(--accent-color);
  color: white;
}

.modal-footer .btn-primary:hover {
  background: var(--accent-hover);
}

.modal-footer .btn-danger {
  background: var(--danger-color, #ef4444);
}

.modal-footer .btn-danger:hover {
  background: var(--danger-hover, #dc2626);
}

/* Responsive Design */
@media (max-width: 640px) {
  .confirmation-modal {
    margin: 16px;
    width: calc(100% - 32px);
  }
  
  .modal-header,
  .modal-body,
  .modal-footer {
    padding-left: 16px;
    padding-right: 16px;
  }
  
  .modal-footer {
    flex-direction: column-reverse;
  }
  
  .modal-footer .btn-secondary,
  .modal-footer .btn-primary {
    width: 100%;
  }
}
