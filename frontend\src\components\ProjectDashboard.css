/* Dashboard Content Areas */
.dashboard-overview,
.dashboard-projects,
.dashboard-timeline {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.dashboard-header {
  margin-bottom: 2rem;
}

.dashboard-header h1 {
  margin: 0 0 0.5rem 0;
  color: #ffffff;
  font-size: 2rem;
  font-weight: 700;
}

.dashboard-header p {
  margin: 0;
  color: #9ca3af;
  font-size: 1.1rem;
  font-weight: 500;
}

.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: #1a1a1a;
  padding: 1.25rem;
  border-radius: 8px;
  border: 1px solid #2d2d2d;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: border-color 0.15s ease;
}

.stat-card:hover {
  border-color: #374151;
}

.stat-icon {
  background: #2d2d2d;
  padding: 0.75rem;
  border-radius: 8px;
  color: #3b82f6;
}

.stat-content {
  display: flex;
  align-items: baseline;
  gap: 10px;
}

.stat-content h3 {
  margin: 0;
  font-size: 2rem;
  font-weight: 700;
  color: #ffffff;
}

.stat-content p {
  margin: 0;
  color: #9ca3af;
  font-weight: 500;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.project-card {
  background: #1a1a1a;
  border-radius: 8px;
  padding: 1.25rem;
  border: 1px solid #2d2d2d;
  cursor: pointer;
  transition: border-color 0.15s ease, box-shadow 0.15s ease, transform 0.15s ease;
}

.project-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transform: translateY(-2px);
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.project-header h3 {
  margin: 0;
  color: #ffffff;
  font-size: 1.25rem;
  font-weight: 600;
}

.project-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: capitalize;
}

.project-description {
  color: #9ca3af;
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.project-progress {
  margin-bottom: 1.5rem;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: #d1d5db;
}

.progress-bar {
  height: 8px;
  background: #2d2d2d;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #3b82f6;
  transition: width 0.3s ease;
  border-radius: 4px;
}

.project-timeline {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.timeline-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #9ca3af;
  font-size: 0.875rem;
}

.project-gantt-mini {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #2d2d2d;
}

.gantt-timeline {
  height: 6px;
  background: #2d2d2d;
  border-radius: 3px;
  overflow: hidden;
}

.gantt-progress {
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 3px;
}

.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  background: #1a1a1a;
  border-radius: 12px;
  border: 1px solid #2d2d2d;
}

.empty-icon {
  color: #6b7280;
  margin-bottom: 1rem;
}

.empty-state h3 {
  margin: 0 0 0.5rem 0;
  color: #ffffff;
  font-size: 1.5rem;
}

.empty-state p {
  margin: 0 0 2rem 0;
  color: #9ca3af;
}

.create-first-project-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: #3b82f6;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 6px;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.15s ease;
}

.create-first-project-btn:hover {
  background: #2563eb;
}

.dashboard-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
  color: #9ca3af;
  background: #0f0f0f;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #2d2d2d;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .dashboard-overview,
  .dashboard-projects,
  .dashboard-timeline {
    padding: 1rem;
  }

  .dashboard-header {
    margin-bottom: 1.5rem;
  }

  .dashboard-header h1 {
    font-size: 1.5rem;
  }

  .projects-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}
